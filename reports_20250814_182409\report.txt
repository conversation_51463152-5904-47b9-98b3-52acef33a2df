============================================================
股债利差回测策略分析报告
============================================================

1. 数据概览
------------------------------
回测时间范围: 2020-01-06 00:00:00 至 2025-05-29 00:00:00
总交易日数: 466 天
股债利差范围: 2.6409 至 7.6458
ETF价格范围: 3.16 至 5.81

2. 策略结果汇总
------------------------------

1年 时间窗口:
  linear 策略:
    总收益率: 0.1644 (16.44%)
    年化收益率: 0.0858 (8.58%)
    最大回撤: -0.0828 (-8.28%)
    夏普比率: 0.5904
    胜率: 0.5204 (52.04%)
    最终价值: 1164444 元
    最优参数: {'sell_threshold': np.float64(0.11146464660815963), 'buy_threshold': np.float64(0.9314431951690285), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03)}

  nonlinear 策略:
    总收益率: 0.1644 (16.44%)
    年化收益率: 0.0858 (8.58%)
    最大回撤: -0.0828 (-8.28%)
    夏普比率: 0.5904
    胜率: 0.5204 (52.04%)
    最终价值: 1164444 元
    最优参数: {'sell_threshold': np.float64(0.11147983580576826), 'buy_threshold': np.float64(0.9314438023262818), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03), 'power': np.float64(1.0)}

  sigmoid 策略:
    总收益率: 0.1634 (16.34%)
    年化收益率: 0.0853 (8.53%)
    最大回撤: -0.0812 (-8.12%)
    夏普比率: 0.5937
    胜率: 0.5204 (52.04%)
    最终价值: 1163434 元
    最优参数: {'sell_center': np.float64(0.09813966808989848), 'buy_center': np.float64(0.9384073030366203), 'sell_ratio': np.float64(0.07881683314389844), 'buy_ratio': np.float64(0.029915045086361173), 'steepness': np.float64(29.477742942248224)}


3年 时间窗口:
  linear 策略:
    总收益率: 0.1666 (16.66%)
    年化收益率: 0.0869 (8.69%)
    最大回撤: -0.0830 (-8.30%)
    夏普比率: 0.5933
    胜率: 0.5204 (52.04%)
    最终价值: 1166649 元
    最优参数: {'sell_threshold': np.float64(0.11147955178483661), 'buy_threshold': np.float64(0.9314479905146162), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03)}

  nonlinear 策略:
    总收益率: 0.1668 (16.68%)
    年化收益率: 0.0870 (8.70%)
    最大回撤: -0.0844 (-8.44%)
    夏普比率: 0.5890
    胜率: 0.5204 (52.04%)
    最终价值: 1166822 元
    最优参数: {'sell_threshold': np.float64(0.11147983716030173), 'buy_threshold': np.float64(0.9283157257234128), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03), 'power': np.float64(1.0)}

  sigmoid 策略:
    总收益率: 0.1653 (16.53%)
    年化收益率: 0.0863 (8.63%)
    最大回撤: -0.0816 (-8.16%)
    夏普比率: 0.5944
    胜率: 0.5204 (52.04%)
    最终价值: 1165321 元
    最优参数: {'sell_center': np.float64(0.09597076133380622), 'buy_center': np.float64(0.9397097771316955), 'sell_ratio': np.float64(0.07814659468301507), 'buy_ratio': np.float64(0.029932009137632165), 'steepness': np.float64(29.839837248884)}


5年 时间窗口:
  linear 策略:
    总收益率: 0.1666 (16.66%)
    年化收益率: 0.0869 (8.69%)
    最大回撤: -0.0830 (-8.30%)
    夏普比率: 0.5933
    胜率: 0.5204 (52.04%)
    最终价值: 1166649 元
    最优参数: {'sell_threshold': np.float64(0.11147955178483661), 'buy_threshold': np.float64(0.9314479905146162), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03)}

  nonlinear 策略:
    总收益率: 0.1668 (16.68%)
    年化收益率: 0.0870 (8.70%)
    最大回撤: -0.0844 (-8.44%)
    夏普比率: 0.5890
    胜率: 0.5204 (52.04%)
    最终价值: 1166822 元
    最优参数: {'sell_threshold': np.float64(0.11147983716030173), 'buy_threshold': np.float64(0.9283157257234128), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03), 'power': np.float64(1.0)}

  sigmoid 策略:
    总收益率: 0.1653 (16.53%)
    年化收益率: 0.0863 (8.63%)
    最大回撤: -0.0816 (-8.16%)
    夏普比率: 0.5944
    胜率: 0.5204 (52.04%)
    最终价值: 1165321 元
    最优参数: {'sell_center': np.float64(0.09597076133380622), 'buy_center': np.float64(0.9397097771316955), 'sell_ratio': np.float64(0.07814659468301507), 'buy_ratio': np.float64(0.029932009137632165), 'steepness': np.float64(29.839837248884)}

3. 最佳策略推荐
------------------------------
推荐策略: 3年 - nonlinear
推荐理由: 综合考虑收益率和风险控制，该策略表现最佳

详细指标:
  总收益率: 16.68%
  年化收益率: 8.70%
  最大回撤: -8.44%
  夏普比率: 0.5890
  胜率: 52.04%
  最终价值: 1166822 元
  相对初始投资收益: 166822 元

最优参数设置:
  sell_threshold: 0.11147983716030173
  buy_threshold: 0.9283157257234128
  sell_ratio: 0.08
  buy_ratio: 0.03
  power: 1.0

============================================================
报告生成完成
============================================================
