# coding=utf-8

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class DataProcessor:
    """数据预处理类，负责读取和清洗股债利差回测所需的数据"""
    
    def __init__(self, data_folder='data'):
        self.data_folder = data_folder
        self.bond_data = None
        self.pe_data = None
        self.index_data = None
        self.merged_data = None
        
    def load_bond_yield_data(self):
        """加载债券收益率数据，提取10年期国债收益率"""
        print("正在加载债券收益率数据...")
        
        # 读取债券收益率数据
        bond_df = pd.read_csv(f'{self.data_folder}/bond_yield_data.csv', encoding='utf-8')
        
        # 筛选出中债国债收益率曲线的数据
        bond_df = bond_df[bond_df['曲线名称'] == '中债国债收益率曲线'].copy()
        
        # 转换日期格式
        bond_df['日期'] = pd.to_datetime(bond_df['日期'])
        
        # 提取10年期收益率
        bond_df = bond_df[['日期', '10年']].copy()
        bond_df.rename(columns={'10年': '十年期国债收益率'}, inplace=True)
        
        # 去除缺失值
        bond_df = bond_df.dropna()
        
        # 按日期排序
        bond_df = bond_df.sort_values('日期').reset_index(drop=True)
        
        self.bond_data = bond_df
        print(f"债券数据加载完成，共{len(bond_df)}条记录")
        return bond_df
    
    def load_pe_data(self):
        """加载市盈率数据，使用滚动市盈率"""
        print("正在加载市盈率数据...")
        
        # 读取市盈率数据
        pe_df = pd.read_csv(f'{self.data_folder}/csi300_pe_data.csv', encoding='utf-8')
        
        # 转换日期格式
        pe_df['日期'] = pd.to_datetime(pe_df['日期'])
        
        # 选择需要的列：日期、指数、滚动市盈率
        pe_df = pe_df[['日期', '指数', '滚动市盈率']].copy()
        
        # 去除缺失值
        pe_df = pe_df.dropna()
        
        # 按日期排序
        pe_df = pe_df.sort_values('日期').reset_index(drop=True)
        
        self.pe_data = pe_df
        print(f"市盈率数据加载完成，共{len(pe_df)}条记录")
        return pe_df
    
    def load_index_data(self):
        """加载指数数据，使用收盘价/1000作为价格"""
        print("正在加载指数数据...")
        
        # 读取指数数据
        index_df = pd.read_csv(f'{self.data_folder}/csi300_index_data.csv', encoding='utf-8')
        
        # 转换日期格式
        index_df['日期'] = pd.to_datetime(index_df['日期'])
        
        # 计算价格（收盘价/1000）
        index_df['价格'] = index_df['收盘'] / 1000
        
        # 选择需要的列
        index_df = index_df[['日期', '收盘', '价格']].copy()
        
        # 去除缺失值
        index_df = index_df.dropna()
        
        # 按日期排序
        index_df = index_df.sort_values('日期').reset_index(drop=True)
        
        self.index_data = index_df
        print(f"指数数据加载完成，共{len(index_df)}条记录")
        return index_df
    
    def merge_data(self):
        """合并所有数据并计算股债利差"""
        print("正在合并数据...")
        
        if self.bond_data is None:
            self.load_bond_yield_data()
        if self.pe_data is None:
            self.load_pe_data()
        if self.index_data is None:
            self.load_index_data()
        
        # 合并数据
        merged = self.pe_data.merge(self.bond_data, on='日期', how='inner')
        merged = merged.merge(self.index_data, on='日期', how='inner')
        
        # 计算股债利差 = 100/市盈率 - 十年期国债收益率
        merged['股债利差'] = (100 / merged['滚动市盈率']) - merged['十年期国债收益率']
        
        # 按日期排序
        merged = merged.sort_values('日期').reset_index(drop=True)
        
        self.merged_data = merged
        print(f"数据合并完成，共{len(merged)}条记录")
        print(f"数据时间范围：{merged['日期'].min()} 至 {merged['日期'].max()}")
        
        return merged
    
    def calculate_percentiles(self, window_years=1):
        """计算股债利差的历史分位数"""
        if self.merged_data is None:
            self.merge_data()
        
        data = self.merged_data.copy()
        window_days = window_years * 252  # 交易日
        
        # 计算滚动分位数
        data[f'股债利差_{window_years}年最小值'] = data['股债利差'].rolling(window=window_days, min_periods=1).min()
        data[f'股债利差_{window_years}年最大值'] = data['股债利差'].rolling(window=window_days, min_periods=1).max()
        
        # 计算分位数
        data[f'股债利差_{window_years}年分位数'] = (data['股债利差'] - data[f'股债利差_{window_years}年最小值']) / \
                                        (data[f'股债利差_{window_years}年最大值'] - data[f'股债利差_{window_years}年最小值'])
        
        # 处理除零情况
        data[f'股债利差_{window_years}年分位数'] = data[f'股债利差_{window_years}年分位数'].fillna(0.5)
        
        return data
    
    # 接收日期参数，并增加默认值，方便调用
    def get_processed_data(self, start_date='2020-01-01', end_date='2025-06-01'):
        """
        获取处理后的完整数据，并根据指定日期范围进行裁剪。
        """
        if self.merged_data is None:
            self.merge_data()
        
        # 计算1年、3年、5年的分位数，这里使用完整数据
        data_1y = self.calculate_percentiles(1)
        data_3y = self.calculate_percentiles(3)
        data_5y = self.calculate_percentiles(5)
        
        # 合并分位数数据
        final_data = data_1y.copy()
        final_data['股债利差_3年分位数'] = data_3y['股债利差_3年分位数']
        final_data['股债利差_5年分位数'] = data_5y['股债利差_5年分位数']
        
        # 将传入的日期字符串转换为日期格式
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        # 在计算完分位数后，根据日期范围裁剪数据
        final_data = final_data[(final_data['日期'] >= start_date) & (final_data['日期'] <= end_date)].copy()
        
        print("\n数据预处理完成！")
        print(f"回测期间共{len(final_data)}条记录")
        print(f"回测时间范围：{final_data['日期'].min()} 至 {final_data['日期'].max()}")
        print("数据概览：")
        print(f"- 股债利差范围：{final_data['股债利差'].min():.4f} 至 {final_data['股债利差'].max():.4f}")
        print(f"- 价格范围：{final_data['价格'].min():.2f} 至 {final_data['价格'].max():.2f}")
        
        return final_data


# coding=utf-8

if __name__ == "__main__":
    # 测试数据处理
    processor = DataProcessor()
    
    # 定义回测日期范围
    start_date = '2020-01-01'
    end_date = '2025-06-01'
    
    # 将日期范围作为参数传递给方法
    data = processor.get_processed_data(start_date=start_date, end_date=end_date)
    data.to_csv('processed_data.csv', index=False, encoding='utf-8-sig')

    print("\n数据样本：")
    print(data.head())
    print(f"\n数据形状：{data.shape}")
    print(f"\n列名：{list(data.columns)}")