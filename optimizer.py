import numpy as np
import pandas as pd
from scipy.optimize import differential_evolution, minimize
from sklearn.model_selection import ParameterGrid
import warnings
warnings.filterwarnings('ignore')

class StrategyOptimizer:
    """策略参数优化器"""
    
    def __init__(self, backtest_engine, data):
        """
        初始化优化器
        
        Args:
            backtest_engine: 回测引擎实例
            data: 回测数据
        """
        self.engine = backtest_engine
        self.data = data
        self.best_results = {}
        
    def objective_function(self, params_list, strategy_func, param_names, percentile_column, 
                           objective='total_return'): # 修改点 1: 移除了 penalty_weight 参数
        """
        目标函数，用于优化
        
        Args:
            params_list: 参数列表
            strategy_func: 策略函数
            param_names: 参数名称列表
            percentile_column: 分位数列名
            objective: 优化目标 ('total_return', 'sharpe_ratio', 'annual_return')
        
        Returns:
            float: 目标函数值（负值，因为优化器是最小化）
        """
        try:
            # 构建参数字典
            params = dict(zip(param_names, params_list))
            
            # 运行回测
            result = self.engine.run_backtest(self.data, strategy_func, params, percentile_column)
            
            # 计算目标值
            if objective == 'total_return':
                objective_value = result['total_return']
            elif objective == 'sharpe_ratio':
                objective_value = result['sharpe_ratio']
            elif objective == 'annual_return':
                objective_value = result['annual_return']
            else:
                objective_value = result['total_return']
            
            # 修改点 1: 移除了整个回撤惩罚的计算
            # drawdown_penalty = abs(result['max_drawdown']) * penalty_weight
            
            # 返回负值（因为优化器最小化），直接返回业绩指标的负数
            return -objective_value
            
        except Exception as e:
            print(f"优化过程中出错: {e}")
            return 1e6  # 返回一个很大的值表示失败
    
    def grid_search_optimization(self, strategy_func, param_grid, percentile_column='股债利差_1年分位数'):
        """
        网格搜索优化
        
        Args:
            strategy_func: 策略函数
            param_grid: 参数网格字典
            percentile_column: 分位数列名
        
        Returns:
            dict: 最优结果
        """
        print(f"开始网格搜索优化 - {strategy_func.__name__}")
        
        best_result = None
        best_score = -np.inf
        
        # 生成参数组合
        param_combinations = list(ParameterGrid(param_grid))
        total_combinations = len(param_combinations)
        
        print(f"总共需要测试 {total_combinations} 种参数组合")
        
        for i, params in enumerate(param_combinations):
            if i % max(1, total_combinations // 10) == 0:
                print(f"进度: {i}/{total_combinations} ({i/total_combinations*100:.1f}%)")
            
            try:
                result = self.engine.run_backtest(self.data, strategy_func, params, percentile_column)
                
                # 修改点 2: 计算综合得分时，只使用总收益率作为唯一标准
                score = result['total_return']
                
                if score > best_score:
                    best_score = score
                    best_result = result
                    
            except Exception as e:
                continue
        
        print(f"网格搜索完成，最佳得分: {best_score:.4f}")
        return best_result
    
    def differential_evolution_optimization(self, strategy_func, param_bounds, param_names, 
                                          percentile_column='股债利差_1年分位数', maxiter=100):
        """
        差分进化算法优化
        
        Args:
            strategy_func: 策略函数
            param_bounds: 参数边界列表 [(min, max), ...]
            param_names: 参数名称列表
            percentile_column: 分位数列名
            maxiter: 最大迭代次数
        
        Returns:
            dict: 最优结果
        """
        print(f"开始差分进化优化 - {strategy_func.__name__}")
        
        # 这里调用的 objective_function 已经是修改后的版本，所以无需改动
        def objective(params_list):
            return self.objective_function(params_list, strategy_func, param_names, percentile_column)
        
        # 运行差分进化算法
        result = differential_evolution(
            objective,
            param_bounds,
            maxiter=maxiter,
            popsize=15,
            seed=42,
            disp=True
        )
        
        # 获取最优参数
        best_params = dict(zip(param_names, result.x))
        
        # 运行最优参数的回测
        best_result = self.engine.run_backtest(self.data, strategy_func, best_params, percentile_column)
        
        print(f"差分进化优化完成，最佳参数: {best_params}")
        return best_result
    
    def optimize_all_strategies(self, window_years_list=[1, 3, 5]):
        """
        优化所有策略和时间窗口组合
        
        Args:
            window_years_list: 时间窗口列表
        
        Returns:
            dict: 所有优化结果
        """
        results = {}
        
        # 定义策略和参数范围 (这部分未作修改)
        strategies = {
            'linear': {
                'func': self.engine.linear_strategy,
                'grid': {
                    'sell_threshold': [0.1, 0.15, 0.2, 0.25, 0.3],
                    'buy_threshold': [0.7, 0.75, 0.8, 0.85, 0.9],
                    'sell_ratio': [0.02, 0.03, 0.04, 0.05, 0.06],
                    'buy_ratio': [0.005, 0.01, 0.015, 0.02, 0.025]
                },
                'bounds': [(0.05, 0.35), (0.65, 0.95), (0.01, 0.08), (0.001, 0.03)],
                'names': ['sell_threshold', 'buy_threshold', 'sell_ratio', 'buy_ratio']
            },
            'nonlinear': {
                'func': self.engine.nonlinear_strategy,
                'grid': {
                    'sell_threshold': [0.1, 0.15, 0.2, 0.25, 0.3],
                    'buy_threshold': [0.7, 0.75, 0.8, 0.85, 0.9],
                    'sell_ratio': [0.02, 0.03, 0.04, 0.05, 0.06],
                    'buy_ratio': [0.005, 0.01, 0.015, 0.02, 0.025],
                    'power': [1.5, 2.0, 2.5, 3.0]
                },
                'bounds': [(0.05, 0.35), (0.65, 0.95), (0.01, 0.08), (0.001, 0.03), (1.0, 4.0)],
                'names': ['sell_threshold', 'buy_threshold', 'sell_ratio', 'buy_ratio', 'power']
            },
            'sigmoid': {
                'func': self.engine.sigmoid_strategy,
                'grid': {
                    'sell_center': [0.05, 0.1, 0.15, 0.2],
                    'buy_center': [0.8, 0.85, 0.9, 0.95],
                    'sell_ratio': [0.02, 0.03, 0.04, 0.05, 0.06],
                    'buy_ratio': [0.005, 0.01, 0.015, 0.02, 0.025],
                    'steepness': [5, 10, 15, 20]
                },
                'bounds': [(0.01, 0.3), (0.7, 0.99), (0.01, 0.08), (0.001, 0.03), (1, 30)],
                'names': ['sell_center', 'buy_center', 'sell_ratio', 'buy_ratio', 'steepness']
            }
        }
        
        # 对每个时间窗口和策略进行优化
        for years in window_years_list:
            percentile_col = f'股债利差_{years}年分位数'
            results[f'{years}年'] = {}
            
            print(f"\n=== 优化 {years} 年时间窗口 ===")
            
            for strategy_name, strategy_config in strategies.items():
                print(f"\n--- 优化策略: {strategy_name} ---")
                
                # 先用网格搜索找到大致范围
                grid_result = self.grid_search_optimization(
                    strategy_config['func'],
                    strategy_config['grid'],
                    percentile_col
                )
                
                # 再用差分进化精细优化
                de_result = self.differential_evolution_optimization(
                    strategy_config['func'],
                    strategy_config['bounds'],
                    strategy_config['names'],
                    percentile_col,
                    maxiter=50
                )
                
                # 选择更好的结果
                if grid_result and de_result:
                    # 这里的比较标准也只看总收益率
                    if grid_result['total_return'] > de_result['total_return']:
                        best_result = grid_result
                        method = 'grid_search'
                    else:
                        best_result = de_result
                        method = 'differential_evolution'
                else:
                    best_result = grid_result or de_result
                    method = 'grid_search' if grid_result else 'differential_evolution'
                
                results[f'{years}年'][strategy_name] = {
                    'result': best_result,
                    'method': method
                }
                
                if best_result:
                    print(f"最佳结果 ({method}):")
                    print(f"  总收益率: {best_result['total_return']:.4f}")
                    print(f"  年化收益率: {best_result['annual_return']:.4f}")
                    print(f"  最大回撤: {best_result['max_drawdown']:.4f}")
                    print(f"  夏普比率: {best_result['sharpe_ratio']:.4f}")
                    print(f"  参数: {best_result['params']}")
        
        self.best_results = results
        return results
    
    def get_best_overall_strategy(self):
        """获取总体最佳策略"""
        if not self.best_results:
            print("请先运行优化")
            return None
        
        best_overall = None
        best_score = -np.inf
        
        for window, strategies in self.best_results.items():
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if result:
                    # 修改点 3: 综合得分只看总收益率，不再考虑回撤和夏普比率
                    score = result['total_return']
                    
                    if score > best_score:
                        best_score = score
                        best_overall = {
                            'window': window,
                            'strategy': strategy_name,
                            'result': result,
                            'method': strategy_data['method'],
                            'score': score
                        }
        
        return best_overall

if __name__ == "__main__":
    # 测试优化器
    # 假设你已经有了 data_processor.py 和 backtest_engine.py 文件
    from data_processor import DataProcessor
    from backtest_engine import BacktestEngine
    
    # 加载数据
    processor = DataProcessor()
    data = processor.get_processed_data()
    
    # 创建回测引擎和优化器
    engine = BacktestEngine()
    optimizer = StrategyOptimizer(engine, data)
    
    # 运行优化（简化版测试）
    print("开始策略优化...")
    results = optimizer.optimize_all_strategies([1])  # 只测试1年窗口
    
    # 获取最佳策略
    best = optimizer.get_best_overall_strategy()
    if best:
        print(f"\n=== 最佳策略 ===")
        print(f"时间窗口: {best['window']}")
        print(f"策略类型: {best['strategy']}")
        print(f"优化方法: {best['method']}")
        print(f"综合得分 (总收益率): {best['score']:.4f}")
        print(f"总收益率: {best['result']['total_return']:.4f}")
        print(f"年化收益率: {best['result']['annual_return']:.4f}")
        print(f"最大回撤: {best['result']['max_drawdown']:.4f}")
        print(f"夏普比率: {best['result']['sharpe_ratio']:.4f}")
        print(f"最佳参数: {best['result']['params']}")