#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股债利差回测系统主程序

该程序实现了基于股债利差的投资策略回测，包括：
1. 数据预处理和清洗
2. 多种策略函数（线性、非线性、Sigmoid）
3. 参数优化（网格搜索、差分进化）
4. 回测结果分析和报告生成

作者: AI Assistant
日期: 2025-01-14
"""

import os
import sys
import time
import warnings
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from data_processor import DataProcessor
from backtest_engine import BacktestEngine
from optimizer import StrategyOptimizer
from report_generator import ReportGenerator
from web_report_generator import WebReportGenerator
from echarts_report_generator import EChartsReportGenerator

warnings.filterwarnings('ignore')

def print_banner():
    """打印程序横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    股债利差回测系统                          ║
    ║                Stock-Bond Spread Backtest System             ║
    ║                                                              ║
    ║  基于"卖4买1"策略的股债利差投资回测与优化系统                ║
    ║  回测期间: 2020年1月1日 - 2025年6月1日                       ║
    ║  初始资金: 50万现金 + 50万ETF                                ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """主函数"""
    print_banner()
    
    start_time = time.time()
    
    try:
        # 第1步：数据预处理
        print("\n" + "="*60)
        print("第1步：数据预处理和清洗")
        print("="*60)
        
        processor = DataProcessor()
        data = processor.get_processed_data()
        
        if data is None or len(data) == 0:
            print("❌ 数据加载失败，请检查data文件夹中的CSV文件")
            return
        
        print(f"✅ 数据预处理完成，共 {len(data)} 条记录")
        
        # 第2步：创建回测引擎
        print("\n" + "="*60)
        print("第2步：初始化回测引擎")
        print("="*60)
        
        engine = BacktestEngine(initial_cash=500000, initial_etf_value=500000)
        print("✅ 回测引擎初始化完成")
        
        # 计算基准收益（买入持有策略）
        benchmark_return = engine.calculate_benchmark_return(data)
        print(f"📊 基准收益率（买入持有）: {benchmark_return:.4f} ({benchmark_return:.2%})")
        
        # 第3步：策略优化
        print("\n" + "="*60)
        print("第3步：策略参数优化")
        print("="*60)
        
        optimizer = StrategyOptimizer(engine, data)
        
        # 运行完整优化（1年、3年、5年时间窗口）
        print("开始运行策略优化，这可能需要几分钟时间...")
        optimization_results = optimizer.optimize_all_strategies([1, 3, 5])
        
        print("✅ 策略优化完成")
        
        # 第4步：获取最佳策略
        print("\n" + "="*60)
        print("第4步：最佳策略分析")
        print("="*60)
        
        best_strategy = optimizer.get_best_overall_strategy()
        
        if best_strategy:
            result = best_strategy['result']
            print(f"🏆 最佳策略组合:")
            print(f"   时间窗口: {best_strategy['window']}")
            print(f"   策略类型: {best_strategy['strategy']}")
            print(f"   优化方法: {best_strategy['method']}")
            print(f"   综合得分: {best_strategy['score']:.4f}")
            print(f"\n📈 关键指标:")
            print(f"   总收益率: {result['total_return']:.4f} ({result['total_return']:.2%})")
            print(f"   年化收益率: {result['annual_return']:.4f} ({result['annual_return']:.2%})")
            print(f"   最大回撤: {result['max_drawdown']:.4f} ({result['max_drawdown']:.2%})")
            print(f"   夏普比率: {result['sharpe_ratio']:.4f}")
            print(f"   胜率: {result['win_rate']:.4f} ({result['win_rate']:.2%})")
            print(f"   最终价值: {result['final_value']:.0f} 元")
            print(f"   绝对收益: {result['final_value'] - 1000000:.0f} 元")
            print(f"\n⚙️  最优参数:")
            for param, value in result['params'].items():
                if isinstance(value, float):
                    print(f"   {param}: {value:.4f}")
                else:
                    print(f"   {param}: {value}")
            
            # 与基准比较
            excess_return = result['total_return'] - benchmark_return
            print(f"\n📊 相对基准表现:")
            print(f"   超额收益: {excess_return:.4f} ({excess_return:.2%})")
            print(f"   基准收益: {benchmark_return:.4f} ({benchmark_return:.2%})")
            
        else:
            print("❌ 未找到有效的最佳策略")
        
        # 第5步：生成报告
        print("\n" + "="*60)
        print("第5步：生成分析报告")
        print("="*60)

        # 生成ECharts Web报告
        echarts_report_generator = EChartsReportGenerator(optimization_results, data, benchmark_return)
        echarts_report_path = f"股债利差回测报告_ECharts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        echarts_report_generator.generate_echarts_report(echarts_report_path)

        # 同时生成传统报告作为备份
        report_generator = ReportGenerator(optimization_results, data, benchmark_return)
        report_dir = f"reports_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(report_dir, exist_ok=True)
        summary_df = report_generator.generate_summary_table()
        summary_df.to_csv(f'{report_dir}/summary_table.csv', index=False, encoding='utf-8-sig')

        print(f"✅ ECharts Web报告已生成: {echarts_report_path}")
        print(f"✅ 数据备份已保存至: {report_dir}")
        print(f"📁 主要输出:")
        print(f"   - {echarts_report_path}: 完整的Web分析报告")
        print(f"   - {report_dir}/summary_table.csv: 结果汇总表")

        # 显示汇总表
        print(f"\n📋 策略结果汇总:")
        print(summary_df.to_string(index=False))
        
        # 第6步：总结
        print("\n" + "="*60)
        print("回测完成总结")
        print("="*60)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"⏱️  总耗时: {elapsed_time:.1f} 秒")
        print(f"📊 测试策略数量: {len([s for strategies in optimization_results.values() for s in strategies.keys()])} 个")
        print(f"📈 数据点数量: {len(data)} 个")
        
        if best_strategy:
            result = best_strategy['result']
            print(f"\n🎯 最终推荐:")
            print(f"   策略: {best_strategy['window']} - {best_strategy['strategy']}")
            print(f"   预期年化收益: {result['annual_return']:.2%}")
            print(f"   风险控制: 最大回撤 {abs(result['max_drawdown']):.2%}")
            print(f"   投资建议: 基于历史数据，该策略在控制风险的前提下获得了较好的收益")
        
        print(f"\n💡 使用建议:")
        print(f"   1. 打开 {echarts_report_path} 查看完整的Web分析报告")
        print(f"   2. 报告包含详细的策略解释、指标说明和投资建议")
        print(f"   3. 参考最优参数进行实际投资决策")
        print(f"   4. 定期重新运行回测以更新策略参数")
        
        print(f"\n⚠️  风险提示:")
        print(f"   历史表现不代表未来收益，投资需谨慎")
        print(f"   建议结合其他分析方法进行投资决策")
        
    except Exception as e:
        print(f"\n❌ 程序运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("启动股债利差回测系统...")
    success = main()
    
    if success:
        print("\n🎉 程序执行成功！")
    else:
        print("\n💥 程序执行失败！")
    
    input("\n按回车键退出...")
