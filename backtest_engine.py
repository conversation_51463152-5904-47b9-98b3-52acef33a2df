import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class BacktestEngine:
    """股债利差回测引擎"""
    
    def __init__(self, initial_cash=500000, initial_etf_value=500000):
        """
        初始化回测引擎
        
        Args:
            initial_cash: 初始现金（默认50万）
            initial_etf_value: 初始ETF价值（默认50万）
        """
        self.initial_cash = initial_cash
        self.initial_etf_value = initial_etf_value
        self.initial_total = initial_cash + initial_etf_value
        
        # 回测结果记录
        self.results = []
        
    def linear_strategy(self, percentile, params):
        """
        线性策略函数
        
        Args:
            percentile: 股债利差分位数 (0-1)
            params: 参数字典 {'sell_threshold': 0.2, 'buy_threshold': 0.8, 'sell_ratio': 0.04, 'buy_ratio': 0.01}
        
        Returns:
            action_ratio: 正数表示买入比例，负数表示卖出比例
        """
        sell_threshold = params.get('sell_threshold', 0.2)
        buy_threshold = params.get('buy_threshold', 0.8)
        sell_ratio = params.get('sell_ratio', 0.04)
        buy_ratio = params.get('buy_ratio', 0.01)
        
        if percentile <= sell_threshold:
            # 低位卖出，线性插值
            ratio = (sell_threshold - percentile) / sell_threshold
            return -sell_ratio * ratio
        elif percentile >= buy_threshold:
            # 高位买入，线性插值
            ratio = (percentile - buy_threshold) / (1 - buy_threshold)
            return buy_ratio * ratio
        else:
            # 中间区域不操作
            return 0
    
    def nonlinear_strategy(self, percentile, params):
        """
        非线性策略函数（指数型）
        
        Args:
            percentile: 股债利差分位数 (0-1)
            params: 参数字典 {'sell_threshold': 0.2, 'buy_threshold': 0.8, 'sell_ratio': 0.04, 'buy_ratio': 0.01, 'power': 2}
        
        Returns:
            action_ratio: 正数表示买入比例，负数表示卖出比例
        """
        sell_threshold = params.get('sell_threshold', 0.2)
        buy_threshold = params.get('buy_threshold', 0.8)
        sell_ratio = params.get('sell_ratio', 0.04)
        buy_ratio = params.get('buy_ratio', 0.01)
        power = params.get('power', 2)
        
        if percentile <= sell_threshold:
            # 低位卖出，非线性
            ratio = ((sell_threshold - percentile) / sell_threshold) ** power
            return -sell_ratio * ratio
        elif percentile >= buy_threshold:
            # 高位买入，非线性
            ratio = ((percentile - buy_threshold) / (1 - buy_threshold)) ** power
            return buy_ratio * ratio
        else:
            # 中间区域不操作
            return 0
    
    def sigmoid_strategy(self, percentile, params):
        """
        Sigmoid策略函数
        
        Args:
            percentile: 股债利差分位数 (0-1)
            params: 参数字典 {'sell_center': 0.1, 'buy_center': 0.9, 'sell_ratio': 0.04, 'buy_ratio': 0.01, 'steepness': 10}
        
        Returns:
            action_ratio: 正数表示买入比例，负数表示卖出比例
        """
        sell_center = params.get('sell_center', 0.1)
        buy_center = params.get('buy_center', 0.9)
        sell_ratio = params.get('sell_ratio', 0.04)
        buy_ratio = params.get('buy_ratio', 0.01)
        steepness = params.get('steepness', 10)
        
        # Sigmoid函数用于卖出（低分位数时）
        sell_signal = 1 / (1 + np.exp(steepness * (percentile - sell_center)))
        
        # Sigmoid函数用于买入（高分位数时）
        buy_signal = 1 / (1 + np.exp(-steepness * (percentile - buy_center)))
        
        # 计算最终操作比例
        if sell_signal > 0.5:
            return -sell_ratio * sell_signal
        elif buy_signal > 0.5:
            return buy_ratio * buy_signal
        else:
            return 0
    
    def run_backtest(self, data, strategy_func, params, percentile_column='股债利差_1年分位数'):
        """
        运行回测
        
        Args:
            data: 包含价格和分位数的数据DataFrame
            strategy_func: 策略函数
            params: 策略参数
            percentile_column: 使用的分位数列名
        
        Returns:
            dict: 回测结果
        """
        # 初始化
        cash = self.initial_cash
        etf_shares = self.initial_etf_value / data.iloc[0]['价格']  # 初始ETF份额
        
        # 记录每日状态
        daily_records = []
        
        for i, row in data.iterrows():
            current_price = row['价格']
            percentile = row[percentile_column]
            
            # 计算当前ETF价值
            etf_value = etf_shares * current_price
            total_value = cash + etf_value
            
            # 获取策略信号
            action_ratio = strategy_func(percentile, params)
            
            # 执行交易
            if action_ratio > 0:  # 买入
                # 用现金买入ETF
                buy_amount = min(cash * action_ratio, cash)  # 不能超过现有现金
                if buy_amount > 0:
                    buy_shares = buy_amount / current_price
                    etf_shares += buy_shares
                    cash -= buy_amount
            elif action_ratio < 0:  # 卖出
                # 卖出ETF获得现金
                sell_ratio = abs(action_ratio)
                sell_shares = etf_shares * sell_ratio
                if sell_shares > 0:
                    sell_amount = sell_shares * current_price
                    etf_shares -= sell_shares
                    cash += sell_amount
            
            # 重新计算总价值
            etf_value = etf_shares * current_price
            total_value = cash + etf_value
            
            # 记录当日状态
            daily_records.append({
                '日期': row['日期'],
                '价格': current_price,
                '分位数': percentile,
                '操作比例': action_ratio,
                '现金': cash,
                'ETF份额': etf_shares,
                'ETF价值': etf_value,
                '总价值': total_value,
                '收益率': (total_value - self.initial_total) / self.initial_total
            })
        
        # 转换为DataFrame
        results_df = pd.DataFrame(daily_records)
        
        # 计算回测指标
        final_value = results_df.iloc[-1]['总价值']
        total_return = (final_value - self.initial_total) / self.initial_total
        
        # 计算年化收益率
        days = len(results_df)
        years = days / 252
        annual_return = (final_value / self.initial_total) ** (1/years) - 1 if years > 0 else 0
        
        # 计算最大回撤
        results_df['累计最高'] = results_df['总价值'].cummax()
        results_df['回撤'] = (results_df['总价值'] - results_df['累计最高']) / results_df['累计最高']
        max_drawdown = results_df['回撤'].min()
        
        # 计算夏普比率（简化版，假设无风险利率为0）
        daily_returns = results_df['收益率'].diff().dropna()
        if len(daily_returns) > 1 and daily_returns.std() > 0:
            sharpe_ratio = daily_returns.mean() / daily_returns.std() * np.sqrt(252)
        else:
            sharpe_ratio = 0
        
        # 计算胜率（基于实际交易信号的成功率）
        trade_results = []
        for i in range(1, len(results_df)):
            if results_df.iloc[i]['操作比例'] != 0:  # 有交易信号
                # 计算交易后的收益
                price_change = (results_df.iloc[i]['价格'] - results_df.iloc[i-1]['价格']) / results_df.iloc[i-1]['价格']
                action = results_df.iloc[i]['操作比例']

                # 买入信号：价格上涨为成功
                # 卖出信号：价格下跌为成功
                if (action > 0 and price_change > 0) or (action < 0 and price_change < 0):
                    trade_results.append(1)  # 成功交易
                else:
                    trade_results.append(0)  # 失败交易

        win_rate = sum(trade_results) / len(trade_results) if len(trade_results) > 0 else 0.5
        
        return {
            'strategy_name': strategy_func.__name__,
            'params': params,
            'percentile_column': percentile_column,
            'final_value': final_value,
            'total_return': total_return,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'win_rate': win_rate,
            'daily_records': results_df
        }
    
    def calculate_benchmark_return(self, data):
        """计算基准收益（买入持有策略）"""
        initial_price = data.iloc[0]['价格']
        final_price = data.iloc[-1]['价格']
        return (final_price - initial_price) / initial_price

