import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class WebReportGenerator:
    """Web报告生成器 - 生成完整的HTML报告页面"""
    
    def __init__(self, optimization_results, data, benchmark_return=None):
        """
        初始化Web报告生成器
        
        Args:
            optimization_results: 优化结果字典
            data: 原始数据
            benchmark_return: 基准收益率
        """
        self.results = optimization_results
        self.data = data
        self.benchmark_return = benchmark_return or 0
        self.best_strategy = self._find_best_strategy()
        
    def _find_best_strategy(self):
        """找到最佳策略"""
        best_overall = None
        best_score = -np.inf
        
        for window, strategies in self.results.items():
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if result:
                    # 综合得分：总收益率 - 回撤惩罚 + 夏普比率加成
                    score = result['total_return'] - abs(result['max_drawdown']) * 0.1 + result['sharpe_ratio'] * 0.1
                    
                    if score > best_score:
                        best_score = score
                        best_overall = {
                            'window': window,
                            'strategy': strategy_name,
                            'result': result,
                            'method': strategy_data['method'],
                            'score': score
                        }
        
        return best_overall
    
    def _create_performance_chart(self):
        """创建业绩表现图表"""
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('净值曲线对比', '回撤分析', '收益率分布', '风险收益散点图'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 准备数据
        all_data = []
        performance_data = []
        
        for window, strategies in self.results.items():
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if result and 'daily_records' in result:
                    daily_records = result['daily_records'].copy()
                    daily_records['策略'] = f"{window}-{strategy_name}"
                    all_data.append(daily_records)
                    
                    performance_data.append({
                        '策略': f"{window}-{strategy_name}",
                        '总收益率': result['total_return'],
                        '年化收益率': result['annual_return'],
                        '最大回撤': abs(result['max_drawdown']),
                        '夏普比率': result['sharpe_ratio'],
                        '胜率': result['win_rate']
                    })
        
        if not all_data:
            return fig
        
        combined_df = pd.concat(all_data, ignore_index=True)
        performance_df = pd.DataFrame(performance_data)
        
        # 1. 净值曲线
        colors = px.colors.qualitative.Set3
        for i, strategy in enumerate(combined_df['策略'].unique()):
            data_subset = combined_df[combined_df['策略'] == strategy]
            is_best = (self.best_strategy and 
                      strategy == f"{self.best_strategy['window']}-{self.best_strategy['strategy']}")
            
            fig.add_trace(
                go.Scatter(
                    x=data_subset['日期'],
                    y=data_subset['总价值'],
                    name=strategy,
                    line=dict(width=4 if is_best else 2, color=colors[i % len(colors)]),
                    showlegend=True
                ),
                row=1, col=1
            )
        
        # 添加基准线
        fig.add_hline(y=1000000, line_dash="dash", line_color="gray", 
                     annotation_text="初始投资", row=1, col=1)
        
        # 2. 回撤分析
        for i, strategy in enumerate(combined_df['策略'].unique()):
            data_subset = combined_df[combined_df['策略'] == strategy]
            is_best = (self.best_strategy and 
                      strategy == f"{self.best_strategy['window']}-{self.best_strategy['strategy']}")
            
            fig.add_trace(
                go.Scatter(
                    x=data_subset['日期'],
                    y=data_subset['回撤'],
                    name=strategy,
                    fill='tonexty' if i == 0 else None,
                    line=dict(width=4 if is_best else 2, color=colors[i % len(colors)]),
                    showlegend=False
                ),
                row=1, col=2
            )
        
        # 3. 收益率分布
        fig.add_trace(
            go.Histogram(
                x=combined_df['收益率'],
                nbinsx=30,
                name='收益率分布',
                marker_color='lightblue',
                showlegend=False
            ),
            row=2, col=1
        )
        
        # 4. 风险收益散点图
        fig.add_trace(
            go.Scatter(
                x=performance_df['最大回撤'],
                y=performance_df['年化收益率'],
                mode='markers+text',
                text=performance_df['策略'],
                textposition="top center",
                marker=dict(
                    size=performance_df['夏普比率'] * 20,
                    color=performance_df['总收益率'],
                    colorscale='Viridis',
                    showscale=True,
                    colorbar=dict(title="总收益率")
                ),
                name='策略表现',
                showlegend=False
            ),
            row=2, col=2
        )
        
        # 更新布局
        fig.update_layout(
            height=800,
            title_text="策略表现综合分析",
            title_x=0.5
        )
        
        # 更新坐标轴标签
        fig.update_xaxes(title_text="日期", row=1, col=1)
        fig.update_yaxes(title_text="总价值 (元)", row=1, col=1)
        fig.update_xaxes(title_text="日期", row=1, col=2)
        fig.update_yaxes(title_text="回撤比例", row=1, col=2)
        fig.update_xaxes(title_text="收益率", row=2, col=1)
        fig.update_yaxes(title_text="频次", row=2, col=1)
        fig.update_xaxes(title_text="最大回撤", row=2, col=2)
        fig.update_yaxes(title_text="年化收益率", row=2, col=2)
        
        return fig
    
    def _create_strategy_signals_chart(self):
        """创建最佳策略信号图表"""
        if not self.best_strategy:
            return go.Figure()
        
        result = self.best_strategy['result']
        if 'daily_records' not in result:
            return go.Figure()
        
        daily_records = result['daily_records']
        
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('价格与股债利差分位数', '买卖信号', '资产配置变化'),
            specs=[[{"secondary_y": True}], [{"secondary_y": False}], [{"secondary_y": False}]],
            vertical_spacing=0.08
        )
        
        # 第一个子图：价格和分位数
        fig.add_trace(
            go.Scatter(
                x=daily_records['日期'],
                y=daily_records['价格'],
                name='ETF价格',
                line=dict(color='blue', width=2)
            ),
            row=1, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=daily_records['日期'],
                y=daily_records['分位数'],
                name='股债利差分位数',
                line=dict(color='red', width=2),
                yaxis='y2'
            ),
            row=1, col=1, secondary_y=True
        )
        
        # 第二个子图：买卖信号
        buy_signals = daily_records[daily_records['操作比例'] > 0]
        sell_signals = daily_records[daily_records['操作比例'] < 0]
        
        if not buy_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=buy_signals['日期'],
                    y=buy_signals['操作比例'],
                    mode='markers',
                    name='买入信号',
                    marker=dict(color='green', size=8, symbol='triangle-up')
                ),
                row=2, col=1
            )
        
        if not sell_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=sell_signals['日期'],
                    y=sell_signals['操作比例'],
                    mode='markers',
                    name='卖出信号',
                    marker=dict(color='red', size=8, symbol='triangle-down')
                ),
                row=2, col=1
            )
        
        fig.add_hline(y=0, line_dash="dash", line_color="gray", row=2, col=1)
        
        # 第三个子图：资产配置
        fig.add_trace(
            go.Scatter(
                x=daily_records['日期'],
                y=daily_records['现金'],
                fill='tonexty',
                name='现金',
                line=dict(color='gold')
            ),
            row=3, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=daily_records['日期'],
                y=daily_records['总价值'],
                fill='tonexty',
                name='ETF价值',
                line=dict(color='lightblue')
            ),
            row=3, col=1
        )
        
        # 更新布局
        fig.update_layout(
            height=900,
            title_text=f"最佳策略详细分析 ({self.best_strategy['window']}-{self.best_strategy['strategy']})",
            title_x=0.5
        )
        
        # 更新坐标轴
        fig.update_xaxes(title_text="日期", row=3, col=1)
        fig.update_yaxes(title_text="ETF价格", row=1, col=1)
        fig.update_yaxes(title_text="分位数", secondary_y=True, row=1, col=1)
        fig.update_yaxes(title_text="操作比例", row=2, col=1)
        fig.update_yaxes(title_text="价值 (元)", row=3, col=1)
        
        return fig

    def _get_strategy_explanation(self, strategy_name):
        """获取策略解释"""
        explanations = {
            'linear': {
                'name': '线性策略',
                'description': '基于股债利差分位数的线性买卖策略，当分位数较低时线性增加卖出比例，分位数较高时线性增加买入比例。',
                'advantages': ['策略逻辑简单清晰', '参数易于理解和调整', '执行成本相对较低'],
                'risks': ['可能无法充分利用极端市场机会', '在震荡市场中可能频繁交易']
            },
            'nonlinear': {
                'name': '非线性策略',
                'description': '使用指数函数调整买卖强度，在极端分位数时加大操作力度，能更好地捕捉市场极端情况。',
                'advantages': ['能更好地利用极端市场机会', '在趋势明确时表现更佳', '风险控制更精细'],
                'risks': ['参数调优相对复杂', '在某些市场环境下可能过度反应']
            },
            'sigmoid': {
                'name': 'Sigmoid策略',
                'description': '使用S型曲线函数，在特定分位数区间内快速响应，提供更平滑的交易信号。',
                'advantages': ['交易信号更平滑', '能避免频繁的小额交易', '适应性较强'],
                'risks': ['参数设置需要更多经验', '在某些情况下响应可能滞后']
            }
        }
        return explanations.get(strategy_name, {
            'name': strategy_name,
            'description': '自定义策略',
            'advantages': ['根据具体实现而定'],
            'risks': ['需要详细分析具体实现']
        })

    def _get_metric_explanation(self, metric):
        """获取指标解释"""
        explanations = {
            'total_return': {
                'name': '总收益率',
                'description': '整个回测期间的累计收益率，反映策略的整体盈利能力。',
                'interpretation': '数值越高越好。正值表示盈利，负值表示亏损。',
                'benchmark': '通常与基准指数收益率比较，超过基准表示策略有效。'
            },
            'annual_return': {
                'name': '年化收益率',
                'description': '将总收益率按年化计算，便于与其他投资产品比较。',
                'interpretation': '反映策略的年均盈利能力，是评估长期投资价值的重要指标。',
                'benchmark': '一般认为年化收益率超过8%为较好水平，超过15%为优秀水平。'
            },
            'max_drawdown': {
                'name': '最大回撤',
                'description': '从最高点到最低点的最大跌幅，反映策略的风险控制能力。',
                'interpretation': '数值越小（绝对值）越好。表示在最坏情况下的最大损失。',
                'benchmark': '一般认为最大回撤控制在10%以内为较好，20%以内为可接受。'
            },
            'sharpe_ratio': {
                'name': '夏普比率',
                'description': '衡量每单位风险获得的超额收益，综合考虑收益和风险。',
                'interpretation': '数值越高越好。大于1为良好，大于2为优秀。',
                'benchmark': '夏普比率是评估策略优劣的核心指标，考虑了风险调整后的收益。'
            },
            'win_rate': {
                'name': '胜率',
                'description': '盈利交易日占总交易日的比例，反映策略的稳定性。',
                'interpretation': '数值越高越好，但需要结合盈亏比一起看。',
                'benchmark': '胜率50%以上表示策略有一定优势，60%以上为较好水平。'
            }
        }
        return explanations.get(metric, {
            'name': metric,
            'description': '自定义指标',
            'interpretation': '需要根据具体定义分析',
            'benchmark': '请参考相关文档'
        })

    def _generate_recommendations(self):
        """生成投资建议"""
        if not self.best_strategy:
            return []

        result = self.best_strategy['result']
        recommendations = []

        # 基于收益率的建议
        if result['annual_return'] > 0.15:
            recommendations.append({
                'type': 'positive',
                'title': '优秀收益表现',
                'content': f"该策略年化收益率达到{result['annual_return']:.2%}，表现优异，建议重点关注。"
            })
        elif result['annual_return'] > 0.08:
            recommendations.append({
                'type': 'positive',
                'title': '良好收益表现',
                'content': f"该策略年化收益率为{result['annual_return']:.2%}，超过大部分理财产品，值得考虑。"
            })
        else:
            recommendations.append({
                'type': 'warning',
                'title': '收益率偏低',
                'content': f"该策略年化收益率为{result['annual_return']:.2%}，建议进一步优化参数或考虑其他策略。"
            })

        # 基于风险的建议
        max_dd = abs(result['max_drawdown'])
        if max_dd < 0.1:
            recommendations.append({
                'type': 'positive',
                'title': '风险控制优秀',
                'content': f"最大回撤仅为{max_dd:.2%}，风险控制能力强，适合稳健型投资者。"
            })
        elif max_dd < 0.2:
            recommendations.append({
                'type': 'neutral',
                'title': '风险控制良好',
                'content': f"最大回撤为{max_dd:.2%}，在可接受范围内，建议设置止损点。"
            })
        else:
            recommendations.append({
                'type': 'warning',
                'title': '注意风险控制',
                'content': f"最大回撤达到{max_dd:.2%}，风险较高，建议降低仓位或优化策略。"
            })

        # 基于夏普比率的建议
        if result['sharpe_ratio'] > 2:
            recommendations.append({
                'type': 'positive',
                'title': '风险调整收益优秀',
                'content': f"夏普比率为{result['sharpe_ratio']:.2f}，风险调整后收益表现优异。"
            })
        elif result['sharpe_ratio'] > 1:
            recommendations.append({
                'type': 'positive',
                'title': '风险调整收益良好',
                'content': f"夏普比率为{result['sharpe_ratio']:.2f}，具有良好的风险收益比。"
            })
        else:
            recommendations.append({
                'type': 'warning',
                'title': '风险收益比待改善',
                'content': f"夏普比率为{result['sharpe_ratio']:.2f}，建议优化策略以提高风险调整后收益。"
            })

        # 基于胜率的建议
        if result['win_rate'] > 0.6:
            recommendations.append({
                'type': 'positive',
                'title': '高胜率策略',
                'content': f"胜率达到{result['win_rate']:.2%}，策略稳定性较好。"
            })
        elif result['win_rate'] > 0.5:
            recommendations.append({
                'type': 'neutral',
                'title': '胜率适中',
                'content': f"胜率为{result['win_rate']:.2%}，建议关注单次盈亏比。"
            })
        else:
            recommendations.append({
                'type': 'warning',
                'title': '胜率偏低',
                'content': f"胜率仅为{result['win_rate']:.2%}，需要提高策略的准确性。"
            })

        # 实施建议
        recommendations.append({
            'type': 'info',
            'title': '实施建议',
            'content': '建议先用小资金测试策略，确认有效后再逐步加大投入。定期回测更新参数，关注市场环境变化。'
        })

        recommendations.append({
            'type': 'warning',
            'title': '风险提示',
            'content': '历史回测结果不代表未来表现，实际投资中可能面临滑点、交易成本等额外风险，请谨慎决策。'
        })

        return recommendations

    def _get_performance_badge(self, value, metric_type):
        """根据指标值获取表现等级徽章"""
        if metric_type == 'annual_return':
            if value >= 0.15:
                return '<span class="performance-badge badge-excellent">优秀</span>'
            elif value >= 0.08:
                return '<span class="performance-badge badge-good">良好</span>'
            elif value >= 0.03:
                return '<span class="performance-badge badge-average">一般</span>'
            else:
                return '<span class="performance-badge badge-poor">较差</span>'
        elif metric_type == 'max_drawdown':
            abs_value = abs(value)
            if abs_value <= 0.05:
                return '<span class="performance-badge badge-excellent">优秀</span>'
            elif abs_value <= 0.10:
                return '<span class="performance-badge badge-good">良好</span>'
            elif abs_value <= 0.20:
                return '<span class="performance-badge badge-average">一般</span>'
            else:
                return '<span class="performance-badge badge-poor">较差</span>'
        elif metric_type == 'sharpe_ratio':
            if value >= 2.0:
                return '<span class="performance-badge badge-excellent">优秀</span>'
            elif value >= 1.0:
                return '<span class="performance-badge badge-good">良好</span>'
            elif value >= 0.5:
                return '<span class="performance-badge badge-average">一般</span>'
            else:
                return '<span class="performance-badge badge-poor">较差</span>'
        elif metric_type == 'win_rate':
            if value >= 0.60:
                return '<span class="performance-badge badge-excellent">优秀</span>'
            elif value >= 0.55:
                return '<span class="performance-badge badge-good">良好</span>'
            elif value >= 0.50:
                return '<span class="performance-badge badge-average">一般</span>'
            else:
                return '<span class="performance-badge badge-poor">较差</span>'
        return ''

    def _generate_all_strategies_comparison(self):
        """生成所有策略的详细对比"""
        if not self.results:
            return ""

        strategies_html = ""

        for window, strategies in self.results.items():
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if not result:
                    continue

                is_best = (self.best_strategy and
                          window == self.best_strategy['window'] and
                          strategy_name == self.best_strategy['strategy'])

                strategy_explanation = self._get_strategy_explanation(strategy_name)
                card_class = "strategy-card best-strategy" if is_best else "strategy-card"
                crown_icon = '<i class="fas fa-crown text-warning"></i> ' if is_best else ''

                strategies_html += f"""
                <div class="{card_class}">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>{crown_icon}{strategy_explanation['name']} ({window}时间窗口)</h5>
                            <p class="text-muted">{strategy_explanation['description']}</p>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-chart-line text-primary"></i> 关键指标</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>年化收益率:</strong> {result['annual_return']:.2%} {self._get_performance_badge(result['annual_return'], 'annual_return')}</li>
                                        <li><strong>最大回撤:</strong> {result['max_drawdown']:.2%} {self._get_performance_badge(result['max_drawdown'], 'max_drawdown')}</li>
                                        <li><strong>夏普比率:</strong> {result['sharpe_ratio']:.2f} {self._get_performance_badge(result['sharpe_ratio'], 'sharpe_ratio')}</li>
                                        <li><strong>胜率:</strong> {result['win_rate']:.2%} {self._get_performance_badge(result['win_rate'], 'win_rate')}</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-cogs text-secondary"></i> 最优参数</h6>
                                    <div class="strategy-params">
                                        {self._format_parameters_detailed(result['params'])}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="metric-card">
                                    <div class="metric-value positive">{result['total_return']:.2%}</div>
                                    <div class="metric-label">总收益率</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value {'positive' if result['final_value'] > 1000000 else 'negative'}">{(result['final_value'] - 1000000):,.0f}元</div>
                                    <div class="metric-label">绝对收益</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                """

        return f"""
        <div class="strategy-comparison">
            <h3><i class="fas fa-balance-scale"></i> 所有策略详细对比</h3>
            <p class="text-muted">以下是所有测试策略的详细表现，标有 <i class="fas fa-crown text-warning"></i> 的为综合表现最佳的策略。</p>
            {strategies_html}
        </div>
        """

    def _format_parameters_detailed(self, params):
        """详细格式化参数显示"""
        param_explanations = {
            'sell_threshold': '卖出阈值 - 股债利差分位数低于此值时开始卖出',
            'buy_threshold': '买入阈值 - 股债利差分位数高于此值时开始买入',
            'sell_ratio': '最大卖出比例 - 单次最多卖出的ETF比例',
            'buy_ratio': '最大买入比例 - 单次最多买入的现金比例',
            'power': '非线性指数 - 控制买卖强度的非线性程度',
            'sell_center': 'Sigmoid卖出中心 - S型函数的卖出中心点',
            'buy_center': 'Sigmoid买入中心 - S型函数的买入中心点',
            'steepness': 'Sigmoid陡峭度 - 控制S型函数的陡峭程度'
        }

        param_html = ""
        for key, value in params.items():
            if isinstance(value, float):
                formatted_value = f"{value:.4f}"
            else:
                formatted_value = str(value)

            explanation = param_explanations.get(key, key)
            param_html += f"""
            <div class="mb-1">
                <span class="tooltip-trigger" title="{explanation}">
                    <strong>{key}:</strong>
                </span>
                <span class="text-primary">{formatted_value}</span>
            </div>
            """
        return param_html

    def _generate_chart_explanations(self):
        """生成图表解释说明"""
        return """
        <div class="explanation-section">
            <h3><i class="fas fa-chart-area"></i> 图表解读指南</h3>

            <div class="chart-explanation">
                <h5><i class="fas fa-line-chart"></i> 净值曲线图</h5>
                <p><strong>作用：</strong>展示不同策略的资产价值变化趋势，直观比较各策略的收益表现。</p>
                <p><strong>解读：</strong></p>
                <ul>
                    <li>曲线越陡峭向上，说明收益率越高</li>
                    <li>曲线越平滑，说明策略越稳定</li>
                    <li>曲线如果出现大幅下跌，说明该时期出现了较大回撤</li>
                    <li>最终高度代表总收益，高于100万元基准线表示盈利</li>
                </ul>
            </div>

            <div class="chart-explanation">
                <h5><i class="fas fa-arrow-down"></i> 回撤分析图</h5>
                <p><strong>作用：</strong>显示策略在不同时期的最大损失情况，评估风险控制能力。</p>
                <p><strong>解读：</strong></p>
                <ul>
                    <li>回撤越接近0%越好，说明没有亏损</li>
                    <li>回撤的深度表示最大亏损幅度</li>
                    <li>回撤的持续时间表示亏损持续的长短</li>
                    <li>回撤恢复的速度反映策略的修复能力</li>
                </ul>
            </div>

            <div class="chart-explanation">
                <h5><i class="fas fa-chart-bar"></i> 收益率分布图</h5>
                <p><strong>作用：</strong>展示日收益率的分布情况，了解策略的收益特征。</p>
                <p><strong>解读：</strong></p>
                <ul>
                    <li>分布越集中在正值区域，说明盈利日数越多</li>
                    <li>分布的宽度反映收益的波动性</li>
                    <li>如果分布偏向右侧（正值），说明策略倾向于盈利</li>
                    <li>极端值的出现频率反映策略的风险水平</li>
                </ul>
            </div>

            <div class="chart-explanation">
                <h5><i class="fas fa-crosshairs"></i> 风险收益散点图</h5>
                <p><strong>作用：</strong>同时展示风险（回撤）和收益的关系，帮助选择最优策略。</p>
                <p><strong>解读：</strong></p>
                <ul>
                    <li>横轴是风险（最大回撤），越往左风险越小</li>
                    <li>纵轴是收益（年化收益率），越往上收益越高</li>
                    <li>圆点大小代表夏普比率，越大表示风险调整后收益越好</li>
                    <li>理想的策略应该位于左上角（低风险高收益）</li>
                </ul>
            </div>

            <div class="chart-explanation">
                <h5><i class="fas fa-exchange-alt"></i> 买卖信号图</h5>
                <p><strong>作用：</strong>展示策略的具体交易时点和强度，了解策略的操作逻辑。</p>
                <p><strong>解读：</strong></p>
                <ul>
                    <li>绿色向上三角形表示买入信号，位置越高买入比例越大</li>
                    <li>红色向下三角形表示卖出信号，位置越低卖出比例越大</li>
                    <li>信号的密集程度反映策略的交易频率</li>
                    <li>结合价格走势可以判断买卖时机是否合理</li>
                </ul>
            </div>

            <div class="chart-explanation">
                <h5><i class="fas fa-pie-chart"></i> 资产配置图</h5>
                <p><strong>作用：</strong>展示现金和ETF资产的配置变化，了解策略的资产管理。</p>
                <p><strong>解读：</strong></p>
                <ul>
                    <li>金色区域代表现金持有量</li>
                    <li>蓝色区域代表ETF持有价值</li>
                    <li>两者的比例变化反映策略的仓位调整</li>
                    <li>总面积的增长表示资产的增值</li>
                </ul>
            </div>
        </div>
        """

    def generate_web_report(self, output_path='stock_bond_spread_report.html'):
        """生成完整的Web报告"""

        # 创建图表
        performance_chart = self._create_performance_chart()
        signals_chart = self._create_strategy_signals_chart()

        # 获取最佳策略信息
        best_result = self.best_strategy['result'] if self.best_strategy else None
        strategy_explanation = self._get_strategy_explanation(self.best_strategy['strategy']) if self.best_strategy else {}
        recommendations = self._generate_recommendations()

        # 生成汇总表数据
        summary_data = []
        for window, strategies in self.results.items():
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if result:
                    summary_data.append({
                        '时间窗口': window,
                        '策略类型': strategy_name,
                        '总收益率': f"{result['total_return']:.2%}",
                        '年化收益率': f"{result['annual_return']:.2%}",
                        '最大回撤': f"{result['max_drawdown']:.2%}",
                        '夏普比率': f"{result['sharpe_ratio']:.2f}",
                        '胜率': f"{result['win_rate']:.2%}",
                        '最终价值': f"{result['final_value']:,.0f}元"
                    })

        # HTML模板
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股债利差回测策略分析报告</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f8f9fa;
        }}
        .header-section {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }}
        .metric-card {{
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }}
        .metric-card:hover {{
            transform: translateY(-2px);
        }}
        .metric-value {{
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }}
        .metric-label {{
            color: #6c757d;
            font-size: 0.9rem;
            cursor: help;
        }}
        .positive {{ color: #28a745; }}
        .negative {{ color: #dc3545; }}
        .neutral {{ color: #ffc107; }}
        .chart-container {{
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .recommendation-card {{
            border-left: 4px solid;
            margin-bottom: 1rem;
        }}
        .recommendation-positive {{ border-left-color: #28a745; }}
        .recommendation-warning {{ border-left-color: #ffc107; }}
        .recommendation-info {{ border-left-color: #17a2b8; }}
        .recommendation-neutral {{ border-left-color: #6c757d; }}
        .strategy-params {{
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }}
        .explanation-section {{
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .strategy-card {{
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s;
        }}
        .strategy-card.best-strategy {{
            border-color: #ffd700;
            background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }}
        .strategy-card:hover {{
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }}
        .tooltip-trigger {{
            cursor: help;
            border-bottom: 1px dotted #6c757d;
        }}
        .chart-explanation {{
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 5px 5px 0;
        }}
        .metric-tooltip {{
            position: relative;
            display: inline-block;
        }}
        .metric-tooltip .tooltiptext {{
            visibility: hidden;
            width: 300px;
            background-color: #333;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.8rem;
            line-height: 1.4;
        }}
        .metric-tooltip .tooltiptext::after {{
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }}
        .metric-tooltip:hover .tooltiptext {{
            visibility: visible;
            opacity: 1;
        }}
        .strategy-comparison {{
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .performance-badge {{
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
            margin-left: 0.5rem;
        }}
        .badge-excellent {{ background-color: #d4edda; color: #155724; }}
        .badge-good {{ background-color: #d1ecf1; color: #0c5460; }}
        .badge-average {{ background-color: #fff3cd; color: #856404; }}
        .badge-poor {{ background-color: #f8d7da; color: #721c24; }}
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1><i class="fas fa-chart-line"></i> 股债利差回测策略分析报告</h1>
                    <p class="lead">基于"卖4买1"策略的股债利差投资回测与优化分析</p>
                    <p><i class="fas fa-calendar"></i> 回测期间: {self.data['日期'].min().strftime('%Y-%m-%d')} 至 {self.data['日期'].max().strftime('%Y-%m-%d')} |
                       <i class="fas fa-database"></i> 数据点: {len(self.data)} 个 |
                       <i class="fas fa-clock"></i> 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 最佳策略概览 -->
        {self._generate_best_strategy_section(best_result, strategy_explanation)}

        <!-- 关键指标 -->
        {self._generate_metrics_section(best_result)}

        <!-- 所有策略详细对比 -->
        {self._generate_all_strategies_comparison()}

        <!-- 策略表现图表 -->
        <div class="chart-container">
            <h3><i class="fas fa-chart-area"></i> 策略表现综合分析</h3>
            <div id="performance-chart"></div>
        </div>

        <!-- 图表解读指南 -->
        {self._generate_chart_explanations()}

        <!-- 最佳策略详细分析 -->
        <div class="chart-container">
            <h3><i class="fas fa-search-plus"></i> 最佳策略详细分析</h3>
            <div id="signals-chart"></div>
        </div>

        <!-- 所有策略对比表 -->
        {self._generate_comparison_table(summary_data)}

        <!-- 指标解释 -->
        {self._generate_metrics_explanation()}

        <!-- 投资建议 -->
        {self._generate_recommendations_section(recommendations)}

        <!-- 免责声明 -->
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle"></i> 重要声明</h5>
            <p>本报告基于历史数据回测生成，仅供参考，不构成投资建议。历史表现不代表未来收益，实际投资存在风险，可能导致本金损失。投资者应根据自身风险承受能力谨慎决策，建议咨询专业投资顾问。</p>
        </div>
    </div>

    <script>
        // 渲染图表
        var performanceChart = {performance_chart.to_json()};
        Plotly.newPlot('performance-chart', performanceChart.data, performanceChart.layout, {{responsive: true}});

        var signalsChart = {signals_chart.to_json()};
        Plotly.newPlot('signals-chart', signalsChart.data, signalsChart.layout, {{responsive: true}});
    </script>
</body>
</html>
        """

        # 保存HTML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_template)

        print(f"✅ Web报告已生成: {output_path}")
        return output_path

    def _generate_best_strategy_section(self, best_result, strategy_explanation):
        """生成最佳策略概览部分"""
        if not best_result or not self.best_strategy:
            return "<div class='alert alert-warning'>未找到最佳策略信息</div>"

        strategy_name = strategy_explanation.get('name', self.best_strategy['strategy'])

        return f"""
        <div class="row mb-4">
            <div class="col-12">
                <div class="explanation-section">
                    <h2><i class="fas fa-trophy text-warning"></i> 最佳策略推荐</h2>
                    <div class="row">
                        <div class="col-md-8">
                            <h4>{strategy_name} ({self.best_strategy['window']}时间窗口)</h4>
                            <p class="text-muted">{strategy_explanation.get('description', '暂无描述')}</p>

                            <div class="strategy-params">
                                <h6><i class="fas fa-cogs"></i> 最优参数设置:</h6>
                                <div class="row">
                                    {self._format_parameters(best_result['params'])}
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-thumbs-up text-success"></i> 策略优势:</h6>
                                    <ul>
                                        {''.join([f'<li>{adv}</li>' for adv in strategy_explanation.get('advantages', [])])}
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-exclamation-circle text-warning"></i> 注意事项:</h6>
                                    <ul>
                                        {''.join([f'<li>{risk}</li>' for risk in strategy_explanation.get('risks', [])])}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="metric-card">
                                    <div class="metric-value positive">{best_result['annual_return']:.2%}</div>
                                    <div class="metric-label">年化收益率</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value {'positive' if best_result['total_return'] > self.benchmark_return else 'negative'}">{(best_result['total_return'] - self.benchmark_return):.2%}</div>
                                    <div class="metric-label">超额收益</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """

    def _format_parameters(self, params):
        """格式化参数显示"""
        param_html = ""
        for key, value in params.items():
            if isinstance(value, float):
                formatted_value = f"{value:.4f}"
            else:
                formatted_value = str(value)

            param_html += f"""
            <div class="col-md-6 mb-2">
                <strong>{key}:</strong> <span class="text-primary">{formatted_value}</span>
            </div>
            """
        return param_html

    def _generate_metrics_section(self, best_result):
        """生成关键指标部分"""
        if not best_result:
            return ""

        total_return_tooltip = "整个回测期间的累计收益率，反映策略的整体盈利能力。正值表示盈利，负值表示亏损。"
        annual_return_tooltip = "将总收益率按年化计算，便于与其他投资产品比较。一般认为年化收益率超过8%为较好水平。"
        max_drawdown_tooltip = "从最高点到最低点的最大跌幅，反映策略的风险控制能力。数值越小（绝对值）越好，一般控制在10%以内为较好。"
        sharpe_ratio_tooltip = "衡量每单位风险获得的超额收益，综合考虑收益和风险。数值越高越好，大于1为良好，大于2为优秀。"
        win_rate_tooltip = "盈利交易日占总交易日的比例，反映策略的稳定性。胜率50%以上表示策略有一定优势。"
        final_value_tooltip = "回测结束时的总资产价值，包括现金和ETF的总价值。"
        absolute_return_tooltip = "相对于初始投资100万元的绝对收益金额。"
        benchmark_tooltip = "买入持有策略的收益率，作为比较基准。超过基准表示策略有效。"

        return f"""
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value positive">{best_result['total_return']:.2%}</div>
                    <div class="metric-label"><i class="fas fa-chart-line"></i> 总收益率</div>
                    <span class="tooltiptext">{total_return_tooltip}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value positive">{best_result['annual_return']:.2%}</div>
                    <div class="metric-label"><i class="fas fa-calendar-alt"></i> 年化收益率</div>
                    <span class="tooltiptext">{annual_return_tooltip}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value {'positive' if abs(best_result['max_drawdown']) < 0.1 else 'negative'}">{best_result['max_drawdown']:.2%}</div>
                    <div class="metric-label"><i class="fas fa-arrow-down"></i> 最大回撤</div>
                    <span class="tooltiptext">{max_drawdown_tooltip}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value {'positive' if best_result['sharpe_ratio'] > 1 else 'negative'}">{best_result['sharpe_ratio']:.2f}</div>
                    <div class="metric-label"><i class="fas fa-balance-scale"></i> 夏普比率</div>
                    <span class="tooltiptext">{sharpe_ratio_tooltip}</span>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value neutral">{best_result['win_rate']:.2%}</div>
                    <div class="metric-label"><i class="fas fa-percentage"></i> 胜率</div>
                    <span class="tooltiptext">{win_rate_tooltip}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value positive">{best_result['final_value']:,.0f}元</div>
                    <div class="metric-label"><i class="fas fa-coins"></i> 最终价值</div>
                    <span class="tooltiptext">{final_value_tooltip}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value positive">{(best_result['final_value'] - 1000000):,.0f}元</div>
                    <div class="metric-label"><i class="fas fa-plus-circle"></i> 绝对收益</div>
                    <span class="tooltiptext">{absolute_return_tooltip}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value {'positive' if best_result['total_return'] > self.benchmark_return else 'negative'}">{self.benchmark_return:.2%}</div>
                    <div class="metric-label"><i class="fas fa-chart-bar"></i> 基准收益</div>
                    <span class="tooltiptext">{benchmark_tooltip}</span>
                </div>
            </div>
        </div>
        """

    def _generate_comparison_table(self, summary_data):
        """生成策略对比表"""
        if not summary_data:
            return ""

        table_rows = ""
        for row in summary_data:
            is_best = (self.best_strategy and
                      row['时间窗口'] == self.best_strategy['window'] and
                      row['策略类型'] == self.best_strategy['strategy'])

            row_class = "table-warning" if is_best else ""
            crown_icon = '<i class="fas fa-crown text-warning"></i> ' if is_best else ''

            table_rows += f"""
            <tr class="{row_class}">
                <td>{crown_icon}{row['时间窗口']}</td>
                <td>{row['策略类型']}</td>
                <td class="text-end">{row['总收益率']}</td>
                <td class="text-end">{row['年化收益率']}</td>
                <td class="text-end">{row['最大回撤']}</td>
                <td class="text-end">{row['夏普比率']}</td>
                <td class="text-end">{row['胜率']}</td>
                <td class="text-end">{row['最终价值']}</td>
            </tr>
            """

        return f"""
        <div class="explanation-section">
            <h3><i class="fas fa-table"></i> 所有策略对比</h3>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>时间窗口</th>
                            <th>策略类型</th>
                            <th class="text-end">总收益率</th>
                            <th class="text-end">年化收益率</th>
                            <th class="text-end">最大回撤</th>
                            <th class="text-end">夏普比率</th>
                            <th class="text-end">胜率</th>
                            <th class="text-end">最终价值</th>
                        </tr>
                    </thead>
                    <tbody>
                        {table_rows}
                    </tbody>
                </table>
            </div>
            <p class="text-muted mt-2">
                <i class="fas fa-info-circle"></i>
                标有 <i class="fas fa-crown text-warning"></i> 的行为推荐的最佳策略
            </p>
        </div>
        """

    def _generate_metrics_explanation(self):
        """生成指标解释部分"""
        metrics = ['total_return', 'annual_return', 'max_drawdown', 'sharpe_ratio', 'win_rate']
        explanations_html = ""

        for metric in metrics:
            explanation = self._get_metric_explanation(metric)
            explanations_html += f"""
            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-info-circle text-primary"></i> {explanation['name']}</h6>
                        <p class="card-text">{explanation['description']}</p>
                        <small class="text-muted">
                            <strong>解读:</strong> {explanation['interpretation']}<br>
                            <strong>基准:</strong> {explanation['benchmark']}
                        </small>
                    </div>
                </div>
            </div>
            """

        return f"""
        <div class="explanation-section">
            <h3><i class="fas fa-question-circle"></i> 指标说明</h3>
            <div class="row">
                {explanations_html}
            </div>
        </div>
        """

    def _generate_recommendations_section(self, recommendations):
        """生成投资建议部分"""
        if not recommendations:
            return ""

        recommendations_html = ""
        for rec in recommendations:
            icon_map = {
                'positive': 'fas fa-check-circle text-success',
                'warning': 'fas fa-exclamation-triangle text-warning',
                'info': 'fas fa-info-circle text-info',
                'neutral': 'fas fa-minus-circle text-secondary'
            }

            icon = icon_map.get(rec['type'], 'fas fa-circle')
            card_class = f"recommendation-card recommendation-{rec['type']}"

            recommendations_html += f"""
            <div class="card {card_class}">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="{icon}"></i> {rec['title']}
                    </h6>
                    <p class="card-text">{rec['content']}</p>
                </div>
            </div>
            """

        return f"""
        <div class="explanation-section">
            <h3><i class="fas fa-lightbulb"></i> 投资建议与风险提示</h3>
            {recommendations_html}
        </div>
        """

if __name__ == "__main__":
    print("Web报告生成器模块已加载")
