﻿时间窗口,策略类型,优化方法,总收益率,年化收益率,最大回撤,夏普比率,胜率,最终价值,参数
1年,linear,differential_evolution,0.1644,0.0858,-0.0828,0.5904,0.5204,1164444,"{'sell_threshold': np.float64(0.11146464660815963), 'buy_threshold': np.float64(0.9314431951690285), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03)}"
1年,nonlinear,differential_evolution,0.1644,0.0858,-0.0828,0.5904,0.5204,1164444,"{'sell_threshold': np.float64(0.11147983580576826), 'buy_threshold': np.float64(0.9314438023262818), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03), 'power': np.float64(1.0)}"
1年,sigmoid,differential_evolution,0.1634,0.0853,-0.0812,0.5937,0.5204,1163434,"{'sell_center': np.float64(0.09813966808989848), 'buy_center': np.float64(0.9384073030366203), 'sell_ratio': np.float64(0.07881683314389844), 'buy_ratio': np.float64(0.029915045086361173), 'steepness': np.float64(29.477742942248224)}"
3年,linear,differential_evolution,0.1666,0.0869,-0.0830,0.5933,0.5204,1166649,"{'sell_threshold': np.float64(0.11147955178483661), 'buy_threshold': np.float64(0.9314479905146162), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03)}"
3年,nonlinear,differential_evolution,0.1668,0.0870,-0.0844,0.5890,0.5204,1166822,"{'sell_threshold': np.float64(0.11147983716030173), 'buy_threshold': np.float64(0.9283157257234128), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03), 'power': np.float64(1.0)}"
3年,sigmoid,differential_evolution,0.1653,0.0863,-0.0816,0.5944,0.5204,1165321,"{'sell_center': np.float64(0.09597076133380622), 'buy_center': np.float64(0.9397097771316955), 'sell_ratio': np.float64(0.07814659468301507), 'buy_ratio': np.float64(0.029932009137632165), 'steepness': np.float64(29.839837248884)}"
5年,linear,differential_evolution,0.1666,0.0869,-0.0830,0.5933,0.5204,1166649,"{'sell_threshold': np.float64(0.11147955178483661), 'buy_threshold': np.float64(0.9314479905146162), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03)}"
5年,nonlinear,differential_evolution,0.1668,0.0870,-0.0844,0.5890,0.5204,1166822,"{'sell_threshold': np.float64(0.11147983716030173), 'buy_threshold': np.float64(0.9283157257234128), 'sell_ratio': np.float64(0.08), 'buy_ratio': np.float64(0.03), 'power': np.float64(1.0)}"
5年,sigmoid,differential_evolution,0.1653,0.0863,-0.0816,0.5944,0.5204,1165321,"{'sell_center': np.float64(0.09597076133380622), 'buy_center': np.float64(0.9397097771316955), 'sell_ratio': np.float64(0.07814659468301507), 'buy_ratio': np.float64(0.029932009137632165), 'steepness': np.float64(29.839837248884)}"
