股债利差回测器

股债利差是一个常见的指标，使用100/指数市盈率 - 十年期国债收益率，得到的一个指标实际上是某种超额收益，当我在高位的时候我应该买，低位的时候卖出，目前我已经开展了一定的调研，发现由于中国股市牛短熊长的特性，导致大部分时间都在低位，所以大概需要卖出4份、买入1份才能实现收支平衡，所以我希望你围绕卖4买1的策略，根据我已有的数据进行回测模拟，找到最适合的投资策略。

回测设置

假设我有50w的现金和50w只上证指数的etf（使用指数除以1000作为价格），通过围绕卖4买1的策略，调整我卖出的比率和买入的比率，告诉我在股债利差不同的时候，找到一个拟合股债利差以及买入卖出策略的函数，使收益最大化，我使用的回测时间范围是2020年1月1日-2025年6月1日。

本地数据

在我本地的data文件夹下，共有3个csv。

- bond_tield_data.csv:数据格式为
  - 曲线名称	日期	3月	6月	1年	3年	5年	7年	10年	30年
    中债中短期票据收益率曲线(AAA)	2015-08-17	2.8421	3.0635	3.1413	3.8164	4.1763	4.3966	4.5112
    中债国债收益率曲线	2015-08-18	2.2395	2.2596	2.2621	2.9249	3.2623	3.5104	3.5126	3.9498
    中债国债收益率曲线	2015-08-19	2.2509	2.2651	2.2571	2.9263	3.2705	3.5119	3.5201	3.9573
    中债中短期票据收益率曲线(AAA)	2015-08-20	2.9167	3.1209	3.2766	3.8142	4.1622	4.3831	4.4753
    中债商业银行普通债收益率曲线(AAA)	2015-08-21	2.9019	3.2123	3.2978	3.733	4.0233	4.2453	4.3874	4.8408
    中债国债收益率曲线	2015-08-24	2.2111	2.247	2.2807	2.9325	3.2399	3.4898	3.4701	3.9402
    中债中短期票据收益率曲线(AAA)	2015-08-25	3.0229	3.2489	3.2754	3.7896	4.1022	4.3344	4.4488
    中债中短期票据收益率曲线(AAA)	2015-08-26	2.9837	3.2316	3.2509	3.8159	4.1402	4.3165	4.4488
    请你筛选出其中的10年期中寨国债收益率曲线的值，作为股债利差的 十年期国债收益率
- csi300_pe_data.csv:数据格式为：
  日期	指数	等权静态市盈率	静态市盈率	静态市盈率中位数	等权滚动市盈率	滚动市盈率	滚动市盈率中位数
  2015-08-17	4077.87	53.61	15.0	40.38	49.23	14.41	35.64
  2015-08-18	3825.41	49.92	14.22	37.9	46.75	13.67	33.24
  2015-08-19	3886.14	50.98	14.32	39.15	47.65	13.75	33.79
  2015-08-20	3761.45	50.0	13.89	38.09	47.06	13.34	32.2
  2015-08-21	3589.54	49.0	13.35	35.79	45.37	12.79	30.93
  2015-08-24	3275.53	44.76	12.2	32.4	42.83	11.63	27.47
  使用滚动市盈率作为市盈率
- csi300_index_data.csv 数据格式为：
  - 日期	开盘	收盘	最高	最低	成交量	成交额	振幅	涨跌幅	涨跌额	换手率
    2015-08-17	4058.11	4077.87	4081.77	4009.99	256939638	336830563044.0	1.76	0.11	4.33	0.78
    2015-08-18	4084.31	3825.41	4103.05	3816.55	319881070	424924184411.0	7.03	-6.19	-252.46	0.98
    2015-08-19	3748.27	3886.14	3898.74	3668.19	269049839	340923470856.0	6.03	1.59	60.73	0.82
    2015-08-20	3848.4	3761.45	3880.82	3761.45	212167965	270576698834.0	3.07	-3.21	-124.69	0.65
    2015-08-21	3714.29	3589.54	3757.78	3578.17	217986403	267498426435.0	4.78	-4.57	-171.91	0.67
    2015-08-24	3454.6	3275.53	3468.15	3266.55	255436652	273124228208.0	5.62	-8.75	-314.01	0.78
    2015-08-25	3070.01	3042.93	3200.11	3019.56	276081306	281160333742.0	5.51	-7.1	-232.6	0.84
  - 使用其中的 （收盘/1000)作为每日的价格。

回测策略设置：

回测的策略很简单：对于某一天，我计算前一天的股债利差的值，并计算从前一天开始向前1年/3年/5年内的最大最小值，如果它在20%以下那么就卖出n%，如果它在80%以上就卖出m%，我需要找到买卖比率关于股债利差这个值的最佳函数，所以需要不断地运行，拟合出一条函数，请你给设置线性、非线性的数学模型，找到对应的这个函数（1年、3年、5年的情况），并把最好情况的收益告诉我，具体算法请你自己设计

请你使用python语言，创建venv，并安装需要的包，我已经给你提供了相应的数据，我需要你不断运行，找到一个最佳的拟合函数，并给我最终的一份报告，得出最佳收益拟合的函数，并给我最佳的收益是多少
