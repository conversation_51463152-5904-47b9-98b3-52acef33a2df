# 股债利差回测策略详细说明

## 📊 策略概述

股债利差回测策略是一种基于股票与债券相对价值的量化投资策略。该策略通过监控股票收益率与债券收益率的差值（股债利差），在股票相对便宜时增加股票仓位，在股票相对昂贵时减少股票仓位，从而实现"低买高卖"的投资目标。

## 🔍 核心原理

### 股债利差的计算
```
股债利差 = 股票收益率 - 债券收益率
股票收益率 = 100 / 市盈率
债券收益率 = 10年期国债收益率
```

### 分位数的含义
分位数表示当前股债利差在历史数据中的相对位置：
- **分位数 = 0%**：当前利差为历史最低值（股票最昂贵）
- **分位数 = 50%**：当前利差为历史中位数
- **分位数 = 100%**：当前利差为历史最高值（股票最便宜）

## 🎯 策略逻辑

### "卖4买1"策略
- **卖出逻辑**：当股债利差分位数较低时（股票相对昂贵），卖出ETF获得现金
- **买入逻辑**：当股债利差分位数较高时（股票相对便宜），用现金买入ETF
- **比例控制**：卖出强度通常是买入强度的4倍左右，体现"卖4买1"的理念

## 🔧 三种策略类型

### 1. 线性策略 (Linear Strategy)

**工作原理**：
```python
if 分位数 <= 卖出阈值:
    卖出比例 = (卖出阈值 - 分位数) / 卖出阈值 * 最大卖出比例
elif 分位数 >= 买入阈值:
    买入比例 = (分位数 - 买入阈值) / (1 - 买入阈值) * 最大买入比例
else:
    不操作
```

**特点**：
- ✅ 响应平稳，操作简单
- ✅ 参数易于理解和调整
- ✅ 适合稳健型投资者
- ❌ 可能无法充分利用极端市场机会

**参数说明**：
- `sell_threshold`：卖出阈值，分位数低于此值时开始卖出
- `buy_threshold`：买入阈值，分位数高于此值时开始买入
- `sell_ratio`：最大卖出比例，单次最多卖出的ETF比例
- `buy_ratio`：最大买入比例，单次最多买入的现金比例

### 2. 非线性策略 (Nonlinear Strategy)

**工作原理**：
```python
if 分位数 <= 卖出阈值:
    比例 = ((卖出阈值 - 分位数) / 卖出阈值) ** power
    卖出比例 = 比例 * 最大卖出比例
elif 分位数 >= 买入阈值:
    比例 = ((分位数 - 买入阈值) / (1 - 买入阈值)) ** power
    买入比例 = 比例 * 最大买入比例
```

**特点**：
- ✅ 在极端情况下反应更强烈
- ✅ 能更好地捕捉市场机会
- ✅ 风险控制更精细
- ❌ 参数调优相对复杂

**额外参数**：
- `power`：非线性指数，控制响应强度的非线性程度

### 3. Sigmoid策略

**工作原理**：
```python
卖出信号 = 1 / (1 + exp(steepness * (分位数 - sell_center)))
买入信号 = 1 / (1 + exp(-steepness * (分位数 - buy_center)))

if 卖出信号 > 0.5:
    卖出比例 = 卖出信号 * 最大卖出比例
elif 买入信号 > 0.5:
    买入比例 = 买入信号 * 最大买入比例
```

**特点**：
- ✅ 提供平滑的非线性响应
- ✅ 避免频繁的小额交易
- ✅ 交易成本较低
- ❌ 参数设置需要更多经验

**特殊参数**：
- `sell_center`：Sigmoid卖出中心点
- `buy_center`：Sigmoid买入中心点
- `steepness`：控制S型函数的陡峭程度

## 📈 回测框架

### 数据处理流程
1. **数据加载**：读取债券收益率、市盈率、指数价格数据
2. **数据清洗**：处理缺失值，统一时间格式
3. **利差计算**：计算股债利差时间序列
4. **分位数计算**：基于1年、3年、5年滚动窗口计算历史分位数

### 回测引擎
1. **初始设置**：50万现金 + 50万ETF
2. **信号生成**：根据策略函数生成买卖信号
3. **交易执行**：按信号比例调整现金和ETF仓位
4. **业绩计算**：记录每日净值、回撤、收益率等指标

### 参数优化
1. **网格搜索**：遍历预定义的参数组合
2. **差分进化**：使用进化算法精细优化
3. **综合评分**：总收益率 - 回撤惩罚 + 夏普比率加成

## 📊 关键指标解释

### 收益指标
- **总收益率**：整个回测期间的累计收益率
- **年化收益率**：按年化计算的收益率，便于比较
- **绝对收益**：相对于初始100万投资的绝对收益金额

### 风险指标
- **最大回撤**：从最高点到最低点的最大跌幅
- **夏普比率**：风险调整后的收益指标，越高越好
- **胜率**：成功交易占总交易次数的比例

### 基准比较
- **基准收益**：买入持有策略的收益率
- **超额收益**：策略收益率减去基准收益率

## 🎯 策略优化目标

### 主要目标
1. **最大化收益**：在可接受风险范围内追求最高收益
2. **控制回撤**：将最大回撤控制在合理范围内
3. **提高夏普比率**：优化风险调整后的收益

### 约束条件
1. **交易成本**：考虑实际交易中的手续费和滑点
2. **流动性**：确保交易规模在市场容量范围内
3. **实施可行性**：参数设置要便于实际执行

## 🔄 策略执行流程

### 日常监控
1. **数据更新**：每日更新股债利差数据
2. **信号计算**：根据最新分位数计算交易信号
3. **仓位调整**：按信号强度调整现金和ETF比例
4. **风险监控**：监控回撤和仓位风险

### 定期维护
1. **参数更新**：定期重新优化策略参数
2. **模型验证**：验证策略在新数据上的表现
3. **风险评估**：评估市场环境变化对策略的影响

## ⚠️ 风险提示

### 模型风险
- 历史回测结果不代表未来表现
- 市场环境变化可能影响策略有效性
- 参数过度拟合可能导致实盘表现不佳

### 执行风险
- 实际交易存在滑点和交易成本
- 市场流动性不足可能影响大额交易
- 系统故障可能导致交易延误

### 市场风险
- 极端市场情况下策略可能失效
- 政策变化可能影响股债相对价值
- 黑天鹅事件可能导致重大损失

## 💡 投资建议

### 实施建议
1. **小资金测试**：建议先用小资金验证策略有效性
2. **分批建仓**：避免一次性大额投入
3. **定期回测**：定期更新参数以适应市场变化
4. **风险控制**：设置合理的止损点和仓位限制

### 适用人群
- **量化投资者**：具备一定量化投资经验的投资者
- **机构投资者**：有专业团队执行策略的机构
- **长期投资者**：能够承受短期波动的长期投资者

---

*本文档仅供学习和研究使用，不构成投资建议。投资有风险，入市需谨慎。*
