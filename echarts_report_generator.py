import pandas as pd
import numpy as np
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class EChartsReportGenerator:
    """基于ECharts的Web报告生成器"""
    
    def __init__(self, optimization_results, data, benchmark_return=None):
        """
        初始化ECharts报告生成器
        
        Args:
            optimization_results: 优化结果字典
            data: 原始数据
            benchmark_return: 基准收益率
        """
        self.results = optimization_results
        self.data = data
        self.benchmark_return = benchmark_return or 0
        self.best_strategy = self._find_best_strategy()
        
    def _find_best_strategy(self):
        """找到最佳策略"""
        best_overall = None
        best_score = -np.inf
        
        for window, strategies in self.results.items():
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if result:
                    # 综合得分：总收益率 - 回撤惩罚 + 夏普比率加成
                    score = result['total_return'] - abs(result['max_drawdown']) * 0.1 + result['sharpe_ratio'] * 0.1
                    
                    if score > best_score:
                        best_score = score
                        best_overall = {
                            'window': window,
                            'strategy': strategy_name,
                            'result': result,
                            'method': strategy_data['method'],
                            'score': score
                        }
        
        return best_overall
    
    def _prepare_chart_data(self):
        """准备图表数据"""
        chart_data = {
            'equity_curves': [],
            'drawdown_data': [],
            'performance_metrics': [],
            'signals_data': None
        }
        
        # 准备净值曲线和回撤数据
        for window, strategies in self.results.items():
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if result and 'daily_records' in result:
                    daily_records = result['daily_records']
                    strategy_label = f"{window}-{strategy_name}"
                    
                    # 净值曲线数据
                    equity_data = []
                    drawdown_data = []
                    
                    for _, row in daily_records.iterrows():
                        date_str = row['日期'].strftime('%Y-%m-%d')
                        equity_data.append([date_str, row['总价值']])
                        drawdown_data.append([date_str, row['回撤'] * 100])  # 转换为百分比
                    
                    chart_data['equity_curves'].append({
                        'name': strategy_label,
                        'data': equity_data,
                        'is_best': (self.best_strategy and 
                                   strategy_label == f"{self.best_strategy['window']}-{self.best_strategy['strategy']}")
                    })
                    
                    chart_data['drawdown_data'].append({
                        'name': strategy_label,
                        'data': drawdown_data,
                        'is_best': (self.best_strategy and 
                                   strategy_label == f"{self.best_strategy['window']}-{self.best_strategy['strategy']}")
                    })
                    
                    # 性能指标数据
                    chart_data['performance_metrics'].append({
                        'name': strategy_label,
                        'annual_return': result['annual_return'] * 100,
                        'max_drawdown': abs(result['max_drawdown']) * 100,
                        'sharpe_ratio': result['sharpe_ratio'],
                        'win_rate': result['win_rate'] * 100,
                        'total_return': result['total_return'] * 100,
                        'is_best': (self.best_strategy and 
                                   strategy_label == f"{self.best_strategy['window']}-{self.best_strategy['strategy']}")
                    })
        
        # 准备最佳策略信号数据
        if self.best_strategy and 'daily_records' in self.best_strategy['result']:
            daily_records = self.best_strategy['result']['daily_records']
            
            price_data = []
            percentile_data = []
            buy_signals = []
            sell_signals = []
            cash_data = []
            etf_data = []
            
            for _, row in daily_records.iterrows():
                date_str = row['日期'].strftime('%Y-%m-%d')
                price_data.append([date_str, row['价格']])
                percentile_data.append([date_str, row['分位数'] * 100])
                cash_data.append([date_str, row['现金']])
                etf_data.append([date_str, row['ETF价值']])
                
                if row['操作比例'] > 0:
                    buy_signals.append([date_str, row['操作比例'] * 100])
                elif row['操作比例'] < 0:
                    sell_signals.append([date_str, abs(row['操作比例']) * 100])
            
            chart_data['signals_data'] = {
                'price_data': price_data,
                'percentile_data': percentile_data,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'cash_data': cash_data,
                'etf_data': etf_data
            }
        
        return chart_data
    
    def _get_strategy_explanation(self, strategy_name):
        """获取策略解释"""
        explanations = {
            'linear': {
                'name': '线性策略',
                'description': '基于股债利差分位数的线性买卖策略。当股债利差分位数较低时（股票相对便宜），线性增加卖出比例；当分位数较高时（股票相对昂贵），线性增加买入比例。',
                'mechanism': '使用线性函数计算买卖比例，响应平稳，适合稳健投资。',
                'advantages': ['策略逻辑简单清晰', '参数易于理解和调整', '执行成本相对较低', '适合长期投资'],
                'risks': ['可能无法充分利用极端市场机会', '在震荡市场中可能频繁交易']
            },
            'nonlinear': {
                'name': '非线性策略',
                'description': '使用指数函数调整买卖强度。在极端分位数时加大操作力度，能更好地捕捉市场极端情况。当市场出现明显的高估或低估时，策略会更积极地进行调仓。',
                'mechanism': '使用幂函数放大极端信号，在市场极端情况下反应更强烈。',
                'advantages': ['能更好地利用极端市场机会', '在趋势明确时表现更佳', '风险控制更精细', '适应市场波动'],
                'risks': ['参数调优相对复杂', '在某些市场环境下可能过度反应']
            },
            'sigmoid': {
                'name': 'Sigmoid策略',
                'description': '使用S型曲线函数，在特定分位数区间内快速响应，提供更平滑的交易信号。这种策略在达到阈值时会迅速调整仓位，但变化过程相对平滑。',
                'mechanism': '使用Sigmoid函数提供平滑的非线性响应，避免突然的大幅调仓。',
                'advantages': ['交易信号更平滑', '能避免频繁的小额交易', '适应性较强', '减少交易成本'],
                'risks': ['参数设置需要更多经验', '在某些情况下响应可能滞后']
            }
        }
        return explanations.get(strategy_name, {
            'name': strategy_name,
            'description': '自定义策略',
            'mechanism': '根据具体实现而定',
            'advantages': ['根据具体实现而定'],
            'risks': ['需要详细分析具体实现']
        })
    
    def _generate_strategy_cards(self):
        """生成策略卡片HTML"""
        if not self.results:
            return ""
        
        cards_html = ""
        
        for window, strategies in self.results.items():
            for strategy_name, strategy_data in strategies.items():
                result = strategy_data['result']
                if not result:
                    continue
                
                is_best = (self.best_strategy and 
                          window == self.best_strategy['window'] and 
                          strategy_name == self.best_strategy['strategy'])
                
                strategy_explanation = self._get_strategy_explanation(strategy_name)
                card_class = "strategy-card best-strategy" if is_best else "strategy-card"
                crown_icon = '<i class="fas fa-crown text-warning"></i> ' if is_best else ''
                
                # 性能等级
                annual_return_badge = self._get_performance_badge(result['annual_return'], 'annual_return')
                drawdown_badge = self._get_performance_badge(result['max_drawdown'], 'max_drawdown')
                sharpe_badge = self._get_performance_badge(result['sharpe_ratio'], 'sharpe_ratio')
                win_rate_badge = self._get_performance_badge(result['win_rate'], 'win_rate')
                
                cards_html += f"""
                <div class="{card_class}">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>{crown_icon}{strategy_explanation['name']} ({window}时间窗口)</h5>
                            <p class="text-muted">{strategy_explanation['description']}</p>
                            <p class="text-info"><strong>机制：</strong>{strategy_explanation['mechanism']}</p>
                            
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-chart-line text-primary"></i> 关键指标</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>年化收益率:</strong> {result['annual_return']:.2%} {annual_return_badge}</li>
                                        <li><strong>最大回撤:</strong> {result['max_drawdown']:.2%} {drawdown_badge}</li>
                                        <li><strong>夏普比率:</strong> {result['sharpe_ratio']:.2f} {sharpe_badge}</li>
                                        <li><strong>胜率:</strong> {result['win_rate']:.2%} {win_rate_badge}</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-thumbs-up text-success"></i> 策略优势</h6>
                                    <ul class="small">
                                        {''.join([f'<li>{adv}</li>' for adv in strategy_explanation['advantages']])}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="metric-card">
                                    <div class="metric-value positive">{result['total_return']:.2%}</div>
                                    <div class="metric-label">总收益率</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value {'positive' if result['final_value'] > 1000000 else 'negative'}">{(result['final_value'] - 1000000):,.0f}元</div>
                                    <div class="metric-label">绝对收益</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                """
        
        return cards_html
    
    def _get_performance_badge(self, value, metric_type):
        """根据指标值获取表现等级徽章"""
        if metric_type == 'annual_return':
            if value >= 0.15:
                return '<span class="performance-badge badge-excellent">优秀</span>'
            elif value >= 0.08:
                return '<span class="performance-badge badge-good">良好</span>'
            elif value >= 0.03:
                return '<span class="performance-badge badge-average">一般</span>'
            else:
                return '<span class="performance-badge badge-poor">较差</span>'
        elif metric_type == 'max_drawdown':
            abs_value = abs(value)
            if abs_value <= 0.05:
                return '<span class="performance-badge badge-excellent">优秀</span>'
            elif abs_value <= 0.10:
                return '<span class="performance-badge badge-good">良好</span>'
            elif abs_value <= 0.20:
                return '<span class="performance-badge badge-average">一般</span>'
            else:
                return '<span class="performance-badge badge-poor">较差</span>'
        elif metric_type == 'sharpe_ratio':
            if value >= 2.0:
                return '<span class="performance-badge badge-excellent">优秀</span>'
            elif value >= 1.0:
                return '<span class="performance-badge badge-good">良好</span>'
            elif value >= 0.5:
                return '<span class="performance-badge badge-average">一般</span>'
            else:
                return '<span class="performance-badge badge-poor">较差</span>'
        elif metric_type == 'win_rate':
            if value >= 0.60:
                return '<span class="performance-badge badge-excellent">优秀</span>'
            elif value >= 0.55:
                return '<span class="performance-badge badge-good">良好</span>'
            elif value >= 0.50:
                return '<span class="performance-badge badge-average">一般</span>'
            else:
                return '<span class="performance-badge badge-poor">较差</span>'
        return ''

    def generate_echarts_report(self, output_path='股债利差回测报告_ECharts.html'):
        """生成基于ECharts的完整Web报告"""

        # 准备图表数据
        chart_data = self._prepare_chart_data()

        # 获取最佳策略信息
        best_result = self.best_strategy['result'] if self.best_strategy else None
        strategy_explanation = self._get_strategy_explanation(self.best_strategy['strategy']) if self.best_strategy else {}

        # 生成策略卡片
        strategy_cards = self._generate_strategy_cards()

        # HTML模板
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股债利差回测策略分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f8f9fa;
        }}
        .header-section {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }}
        .metric-card {{
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
            position: relative;
        }}
        .metric-card:hover {{
            transform: translateY(-2px);
        }}
        .metric-value {{
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }}
        .metric-label {{
            color: #6c757d;
            font-size: 0.9rem;
            cursor: help;
        }}
        .positive {{ color: #28a745; }}
        .negative {{ color: #dc3545; }}
        .neutral {{ color: #ffc107; }}
        .chart-container {{
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .strategy-card {{
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s;
        }}
        .strategy-card.best-strategy {{
            border-color: #ffd700;
            background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }}
        .strategy-card:hover {{
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }}
        .performance-badge {{
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
            margin-left: 0.5rem;
        }}
        .badge-excellent {{ background-color: #d4edda; color: #155724; }}
        .badge-good {{ background-color: #d1ecf1; color: #0c5460; }}
        .badge-average {{ background-color: #fff3cd; color: #856404; }}
        .badge-poor {{ background-color: #f8d7da; color: #721c24; }}
        .chart-explanation {{
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 5px 5px 0;
        }}
        .explanation-section {{
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .metric-tooltip {{
            position: relative;
            display: inline-block;
        }}
        .metric-tooltip .tooltiptext {{
            visibility: hidden;
            width: 300px;
            background-color: #333;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.8rem;
            line-height: 1.4;
        }}
        .metric-tooltip .tooltiptext::after {{
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }}
        .metric-tooltip:hover .tooltiptext {{
            visibility: visible;
            opacity: 1;
        }}
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1><i class="fas fa-chart-line"></i> 股债利差回测策略分析报告</h1>
                    <p class="lead">基于"卖4买1"策略的股债利差投资回测与优化分析</p>
                    <p><i class="fas fa-calendar"></i> 回测期间: {self.data['日期'].min().strftime('%Y-%m-%d')} 至 {self.data['日期'].max().strftime('%Y-%m-%d')} |
                       <i class="fas fa-database"></i> 数据点: {len(self.data)} 个 |
                       <i class="fas fa-clock"></i> 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 最佳策略概览 -->
        {self._generate_best_strategy_section(best_result, strategy_explanation)}

        <!-- 关键指标 -->
        {self._generate_metrics_section(best_result)}

        <!-- 所有策略详细对比 -->
        <div class="explanation-section">
            <h3><i class="fas fa-balance-scale"></i> 所有策略详细对比</h3>
            <p class="text-muted">以下是所有测试策略的详细表现，标有 <i class="fas fa-crown text-warning"></i> 的为综合表现最佳的策略。</p>
            {strategy_cards}
        </div>

        <!-- 策略表现图表 -->
        <div class="chart-container">
            <h3><i class="fas fa-chart-area"></i> 策略表现综合分析</h3>
            <div class="chart-explanation">
                <p><strong>图表说明：</strong>以下图表展示了不同策略的净值曲线、回撤情况和风险收益特征。净值曲线越陡峭向上表示收益越高，回撤越小表示风险控制越好。</p>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div id="equityChart" style="height: 400px;"></div>
                </div>
                <div class="col-md-6">
                    <div id="drawdownChart" style="height: 400px;"></div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <div id="performanceChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>

        <!-- 最佳策略详细分析 -->
        <div class="chart-container">
            <h3><i class="fas fa-search-plus"></i> 最佳策略详细分析</h3>
            <div class="chart-explanation">
                <p><strong>信号分析：</strong>以下图表展示了最佳策略的具体交易信号和资产配置变化。绿色表示买入信号，红色表示卖出信号。</p>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div id="signalsChart" style="height: 400px;"></div>
                </div>
                <div class="col-md-6">
                    <div id="allocationChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>

        <!-- 策略解读指南 -->
        {self._generate_strategy_guide()}

        <!-- 免责声明 -->
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle"></i> 重要声明</h5>
            <p>本报告基于历史数据回测生成，仅供参考，不构成投资建议。历史表现不代表未来收益，实际投资存在风险，可能导致本金损失。投资者应根据自身风险承受能力谨慎决策，建议咨询专业投资顾问。</p>
        </div>
    </div>

    <script>
        // 图表数据
        const chartData = {json.dumps(chart_data, ensure_ascii=False, default=str)};

        // 初始化图表
        {self._generate_chart_scripts()}
    </script>
</body>
</html>
        """

        # 保存HTML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_template)

        print(f"✅ ECharts Web报告已生成: {output_path}")
        return output_path

    def _generate_best_strategy_section(self, best_result, strategy_explanation):
        """生成最佳策略概览部分"""
        if not best_result or not self.best_strategy:
            return "<div class='alert alert-warning'>未找到最佳策略信息</div>"

        strategy_name = strategy_explanation.get('name', self.best_strategy['strategy'])

        return f"""
        <div class="row mb-4">
            <div class="col-12">
                <div class="explanation-section">
                    <h2><i class="fas fa-trophy text-warning"></i> 最佳策略推荐</h2>
                    <div class="row">
                        <div class="col-md-8">
                            <h4>{strategy_name} ({self.best_strategy['window']}时间窗口)</h4>
                            <p class="text-muted">{strategy_explanation.get('description', '暂无描述')}</p>
                            <p class="text-info"><strong>工作机制：</strong>{strategy_explanation.get('mechanism', '暂无说明')}</p>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-thumbs-up text-success"></i> 策略优势:</h6>
                                    <ul>
                                        {''.join([f'<li>{adv}</li>' for adv in strategy_explanation.get('advantages', [])])}
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-exclamation-circle text-warning"></i> 注意事项:</h6>
                                    <ul>
                                        {''.join([f'<li>{risk}</li>' for risk in strategy_explanation.get('risks', [])])}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="metric-card">
                                    <div class="metric-value positive">{best_result['annual_return']:.2%}</div>
                                    <div class="metric-label">年化收益率</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value {'positive' if best_result['total_return'] > self.benchmark_return else 'negative'}">{(best_result['total_return'] - self.benchmark_return):.2%}</div>
                                    <div class="metric-label">超额收益</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """

    def _generate_metrics_section(self, best_result):
        """生成关键指标部分"""
        if not best_result:
            return ""

        return f"""
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value positive">{best_result['total_return']:.2%}</div>
                    <div class="metric-label"><i class="fas fa-chart-line"></i> 总收益率</div>
                    <span class="tooltiptext">整个回测期间的累计收益率，反映策略的整体盈利能力。正值表示盈利，负值表示亏损。</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value positive">{best_result['annual_return']:.2%}</div>
                    <div class="metric-label"><i class="fas fa-calendar-alt"></i> 年化收益率</div>
                    <span class="tooltiptext">将总收益率按年化计算，便于与其他投资产品比较。一般认为年化收益率超过8%为较好水平。</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value {'positive' if abs(best_result['max_drawdown']) < 0.1 else 'negative'}">{best_result['max_drawdown']:.2%}</div>
                    <div class="metric-label"><i class="fas fa-arrow-down"></i> 最大回撤</div>
                    <span class="tooltiptext">从最高点到最低点的最大跌幅，反映策略的风险控制能力。数值越小（绝对值）越好。</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value {'positive' if best_result['sharpe_ratio'] > 1 else 'negative'}">{best_result['sharpe_ratio']:.2f}</div>
                    <div class="metric-label"><i class="fas fa-balance-scale"></i> 夏普比率</div>
                    <span class="tooltiptext">衡量每单位风险获得的超额收益，综合考虑收益和风险。数值越高越好，大于1为良好。</span>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value neutral">{best_result['win_rate']:.2%}</div>
                    <div class="metric-label"><i class="fas fa-percentage"></i> 胜率</div>
                    <span class="tooltiptext">成功交易占总交易次数的比例，反映策略的准确性。胜率50%以上表示策略有一定优势。</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value positive">{best_result['final_value']:,.0f}元</div>
                    <div class="metric-label"><i class="fas fa-coins"></i> 最终价值</div>
                    <span class="tooltiptext">回测结束时的总资产价值，包括现金和ETF的总价值。</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value positive">{(best_result['final_value'] - 1000000):,.0f}元</div>
                    <div class="metric-label"><i class="fas fa-plus-circle"></i> 绝对收益</div>
                    <span class="tooltiptext">相对于初始投资100万元的绝对收益金额。</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-tooltip">
                    <div class="metric-value {'positive' if best_result['total_return'] > self.benchmark_return else 'negative'}">{self.benchmark_return:.2%}</div>
                    <div class="metric-label"><i class="fas fa-chart-bar"></i> 基准收益</div>
                    <span class="tooltiptext">买入持有策略的收益率，作为比较基准。超过基准表示策略有效。</span>
                </div>
            </div>
        </div>
        """

    def _generate_strategy_guide(self):
        """生成策略解读指南"""
        return """
        <div class="explanation-section">
            <h3><i class="fas fa-book"></i> 策略解读指南</h3>

            <div class="chart-explanation">
                <h5><i class="fas fa-question-circle"></i> 什么是股债利差策略？</h5>
                <p>股债利差策略是一种基于股票收益率与债券收益率差值的投资策略。当股债利差较小时（股票相对昂贵），减少股票仓位；当股债利差较大时（股票相对便宜），增加股票仓位。</p>
                <p><strong>核心逻辑：</strong>股债利差 = 股票收益率（1/市盈率）- 债券收益率。分位数表示当前利差在历史上的相对位置。</p>
            </div>

            <div class="chart-explanation">
                <h5><i class="fas fa-cogs"></i> 三种策略类型对比</h5>
                <div class="row">
                    <div class="col-md-4">
                        <h6>线性策略</h6>
                        <p><strong>特点：</strong>响应平稳，操作简单</p>
                        <p><strong>适用：</strong>稳健型投资者</p>
                        <p><strong>优势：</strong>风险可控，易于理解</p>
                    </div>
                    <div class="col-md-4">
                        <h6>非线性策略</h6>
                        <p><strong>特点：</strong>极端情况下反应更强烈</p>
                        <p><strong>适用：</strong>追求更高收益的投资者</p>
                        <p><strong>优势：</strong>能更好地捕捉市场机会</p>
                    </div>
                    <div class="col-md-4">
                        <h6>Sigmoid策略</h6>
                        <p><strong>特点：</strong>平滑的非线性响应</p>
                        <p><strong>适用：</strong>希望减少交易频率的投资者</p>
                        <p><strong>优势：</strong>交易成本较低</p>
                    </div>
                </div>
            </div>

            <div class="chart-explanation">
                <h5><i class="fas fa-chart-line"></i> 如何解读图表</h5>
                <ul>
                    <li><strong>净值曲线：</strong>显示资产价值变化，曲线越陡峭向上收益越高</li>
                    <li><strong>回撤图：</strong>显示最大亏损情况，回撤越小风险控制越好</li>
                    <li><strong>风险收益图：</strong>横轴是风险，纵轴是收益，左上角的策略最优</li>
                    <li><strong>交易信号：</strong>绿色表示买入，红色表示卖出，大小表示操作强度</li>
                </ul>
            </div>

            <div class="chart-explanation">
                <h5><i class="fas fa-lightbulb"></i> 投资建议</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>实施建议</h6>
                        <ul>
                            <li>建议先用小资金测试策略</li>
                            <li>定期回测更新参数</li>
                            <li>关注市场环境变化</li>
                            <li>设置合理的止损点</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>风险提示</h6>
                        <ul>
                            <li>历史回测不代表未来表现</li>
                            <li>实际交易存在滑点和成本</li>
                            <li>需要考虑税收影响</li>
                            <li>建议分散投资降低风险</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        """

    def _generate_chart_scripts(self):
        """生成ECharts图表脚本"""
        return """
        // 净值曲线图
        const equityChart = echarts.init(document.getElementById('equityChart'));
        const equityOption = {
            title: { text: '净值曲线对比', left: 'center' },
            tooltip: { trigger: 'axis' },
            legend: { top: 30 },
            xAxis: { type: 'time' },
            yAxis: {
                type: 'value',
                axisLabel: { formatter: '{value}万' }
            },
            series: chartData.equity_curves.map(item => ({
                name: item.name,
                type: 'line',
                data: item.data.map(d => [d[0], d[1]/10000]),
                lineStyle: { width: item.is_best ? 4 : 2 },
                emphasis: { focus: 'series' }
            }))
        };
        equityChart.setOption(equityOption);

        // 回撤分析图
        const drawdownChart = echarts.init(document.getElementById('drawdownChart'));
        const drawdownOption = {
            title: { text: '回撤分析', left: 'center' },
            tooltip: { trigger: 'axis', formatter: '{b}<br/>{a}: {c}%' },
            legend: { top: 30 },
            xAxis: { type: 'time' },
            yAxis: {
                type: 'value',
                axisLabel: { formatter: '{value}%' }
            },
            series: chartData.drawdown_data.map(item => ({
                name: item.name,
                type: 'line',
                data: item.data,
                areaStyle: { opacity: 0.3 },
                lineStyle: { width: item.is_best ? 4 : 2 }
            }))
        };
        drawdownChart.setOption(drawdownOption);

        // 风险收益散点图
        const performanceChart = echarts.init(document.getElementById('performanceChart'));
        const performanceOption = {
            title: { text: '风险收益分析', left: 'center' },
            tooltip: {
                trigger: 'item',
                formatter: '{a}<br/>{b}<br/>年化收益: {c[1]}%<br/>最大回撤: {c[0]}%<br/>夏普比率: {c[2]}'
            },
            xAxis: {
                name: '最大回撤 (%)',
                type: 'value',
                axisLabel: { formatter: '{value}%' }
            },
            yAxis: {
                name: '年化收益率 (%)',
                type: 'value',
                axisLabel: { formatter: '{value}%' }
            },
            series: [{
                name: '策略表现',
                type: 'scatter',
                data: chartData.performance_metrics.map(item => [
                    item.max_drawdown,
                    item.annual_return,
                    item.sharpe_ratio,
                    item.name
                ]),
                symbolSize: function(data) { return Math.max(data[2] * 15, 10); },
                itemStyle: {
                    color: function(params) {
                        const item = chartData.performance_metrics[params.dataIndex];
                        return item.is_best ? '#FFD700' : '#5470C6';
                    }
                },
                label: {
                    show: true,
                    position: 'top',
                    formatter: '{@[3]}'
                }
            }]
        };
        performanceChart.setOption(performanceOption);

        // 交易信号图
        if (chartData.signals_data) {
            const signalsChart = echarts.init(document.getElementById('signalsChart'));
            const signalsOption = {
                title: { text: '交易信号分析', left: 'center' },
                tooltip: { trigger: 'axis' },
                legend: { top: 30 },
                xAxis: { type: 'time' },
                yAxis: [
                    { type: 'value', name: 'ETF价格' },
                    { type: 'value', name: '分位数(%)', min: 0, max: 100 }
                ],
                series: [
                    {
                        name: 'ETF价格',
                        type: 'line',
                        data: chartData.signals_data.price_data,
                        yAxisIndex: 0
                    },
                    {
                        name: '股债利差分位数',
                        type: 'line',
                        data: chartData.signals_data.percentile_data,
                        yAxisIndex: 1,
                        lineStyle: { color: '#FF6B6B' }
                    },
                    {
                        name: '买入信号',
                        type: 'scatter',
                        data: chartData.signals_data.buy_signals,
                        symbolSize: 8,
                        itemStyle: { color: '#4ECDC4' },
                        yAxisIndex: 1
                    },
                    {
                        name: '卖出信号',
                        type: 'scatter',
                        data: chartData.signals_data.sell_signals,
                        symbolSize: 8,
                        itemStyle: { color: '#FF6B6B' },
                        yAxisIndex: 1
                    }
                ]
            };
            signalsChart.setOption(signalsOption);

            // 资产配置图
            const allocationChart = echarts.init(document.getElementById('allocationChart'));
            const allocationOption = {
                title: { text: '资产配置变化', left: 'center' },
                tooltip: { trigger: 'axis' },
                legend: { top: 30 },
                xAxis: { type: 'time' },
                yAxis: {
                    type: 'value',
                    axisLabel: { formatter: '{value}万' }
                },
                series: [
                    {
                        name: '现金',
                        type: 'line',
                        data: chartData.signals_data.cash_data.map(d => [d[0], d[1]/10000]),
                        areaStyle: { color: '#FFD700', opacity: 0.6 },
                        stack: 'total'
                    },
                    {
                        name: 'ETF价值',
                        type: 'line',
                        data: chartData.signals_data.etf_data.map(d => [d[0], d[1]/10000]),
                        areaStyle: { color: '#87CEEB', opacity: 0.6 },
                        stack: 'total'
                    }
                ]
            };
            allocationChart.setOption(allocationOption);
        }

        // 响应式调整
        window.addEventListener('resize', function() {
            equityChart.resize();
            drawdownChart.resize();
            performanceChart.resize();
            if (chartData.signals_data) {
                document.getElementById('signalsChart') && echarts.getInstanceByDom(document.getElementById('signalsChart')).resize();
                document.getElementById('allocationChart') && echarts.getInstanceByDom(document.getElementById('allocationChart')).resize();
            }
        });
        """

if __name__ == "__main__":
    print("ECharts报告生成器模块已加载")
